{"name": "ibdown", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "repository": {"type": "git", "url": "git+https://github.com/BrowserMCP/mcp.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/BrowserMCP/mcp/issues"}, "homepage": "https://github.com/BrowserMCP/mcp#readme", "description": "", "dependencies": {"playwright": "^1.51.1"}}