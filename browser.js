/**
 * Browser setup and control for the Interactive Brokers statement downloader
 */

const { chromium } = require('playwright');
const { BROWSER_CONFIG, downloadsDir, URLS } = require('./config');

/**
 * Sets up the browser and navigates to the start page
 * @returns {Object} Object containing browser, context, and page
 */
async function setupBrowser() {
  // Launch the browser
  const browser = await chromium.launch(BROWSER_CONFIG);
  
  // Create a new context with download options
  const context = await browser.newContext({
    acceptDownloads: true,
    downloadsPath: downloadsDir
  });
  
  // Create a new page
  const page = await context.newPage();
  
  // Navigate to a starting page
  await page.goto(URLS.START_PAGE);
  
  return { browser, context, page };
}

/**
 * Closes the browser
 * @param {Object} browser - Browser instance to close
 */
async function closeBrowser(browser) {
  await browser.close();
}

module.exports = {
  setupBrowser,
  closeBrowser
};
