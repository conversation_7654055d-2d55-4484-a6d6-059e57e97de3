/**
 * Interactive Brokers Statement Downloader
 *
 * This application automates the process of downloading daily statements
 * from Interactive Brokers for a specified date range.
 */

const readline = require('readline');
const { DATE_RANGE, downloadsDir } = require('./config');
const { createLogger, getBusinessDaysBetweenDates, sleep } = require('./utils');
const { setupBrowser, closeBrowser } = require('./browser');
const { downloadStatement } = require('./downloader');
const { createProgressTracker } = require('./progress');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Main function that runs the application
 */
async function run() {
  console.log('Launching browser...');

  // Set up the browser
  const { browser, page } = await setupBrowser();

  console.log('\n---------------------------------------------');
  console.log('Browser is ready! You can now interact with it.');
  console.log('Please log in to Interactive Brokers.');
  console.log('When you\'re done logging in, press Enter to');
  console.log('start the automated statement download process.');
  console.log('---------------------------------------------\n');

  // Wait for user to log in
  await new Promise(resolve => rl.question('Press Enter when you\'ve logged in...', resolve));

  // Generate business days from start date to end date
  const dates = getBusinessDaysBetweenDates(DATE_RANGE.startDate, DATE_RANGE.endDate);

  console.log(`Found ${dates.length} business days to process.`);
  console.log('First few dates:', dates.slice(0, 5).join(', '), '...');

  if (dates.length > 10) {
    console.log('Last few dates:', dates.slice(-5).join(', '), '...');
  }

  // Ask for confirmation before proceeding
  await new Promise(resolve => {
    rl.question('\nPress Enter to start downloading statements for these dates...', resolve);
  });

  // Create logger
  const logger = createLogger();

  // Create progress tracker
  const progress = createProgressTracker(dates, logger);

  // Set up keyboard listeners for pause/resume and quit
  progress.setupKeyboardListeners();

  // Process each date
  while (progress.currentIndex < progress.totalDates) {
    if (progress.isPaused) {
      // If paused, wait 1 second before checking again
      await sleep(1000);
      continue;
    }

    const date = dates[progress.currentIndex];
    logger.log(`\nProcessing date ${progress.currentIndex + 1}/${progress.totalDates}: ${date}`);

    const result = await downloadStatement(page, date, logger);
    progress.incrementCompleted(result.success, result.noStatementAvailable);

    // Show progress every 5 downloads or at the end
    if (progress.completed % 5 === 0 || progress.completed === progress.totalDates) {
      progress.showProgress();
    }

    progress.incrementIndex();
  }

  // Reset keyboard handling
  progress.resetKeyboardHandling();

  console.log('\n---------------------------------------------');
  console.log('All statements have been processed!');
  console.log(`Total dates processed: ${progress.completed}`);
  console.log(`Successful downloads: ${progress.successful}`);
  console.log(`Dates with no statements available: ${progress.noStatementDates}`);
  console.log(`Files saved to: ${downloadsDir}`);
  console.log('---------------------------------------------\n');

  // Ask if user wants to close the browser
  await new Promise(resolve => {
    rl.question('Press Enter to close the browser...', async () => {
      resolve();
    });
  });

  // Close the browser
  await closeBrowser(browser);
  rl.close();

  console.log('\nBrowser closed. Goodbye!');
}

// Run the main function and handle errors
run().catch(error => {
  console.error('An error occurred:', error);
  process.exit(1);
});
