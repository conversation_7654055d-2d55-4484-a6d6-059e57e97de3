<!DOCTYPE html><html ng-app="amApp" dir="ltr" lang="en" class="_lwidget sb-init sb-lock" data-theme="light" style="--padding-top: 163.484375px; --padding-right: 0px; --padding-bottom: 0px; --padding-left: 0px;"><head><style type="text/css">@charset "UTF-8";[ng\:cloak],[ng-cloak],[data-ng-cloak],[x-ng-cloak],.ng-cloak,.x-ng-cloak,.ng-hide:not(.ng-hide-animate){display:none !important;}ng\:form{display:block;}.ng-animate-shim{visibility:hidden;}.ng-anchor{position:absolute;}</style>
        <title>
            
                Account Management
            
        </title>
    
        


























 


    
    
    
        
        
        
                
    




	
        
    
    
     
    




<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="expires" content="0">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0, minimal-ui">
<meta name="google" content="notranslate">
<meta name="HandheldFriendly" content="true">
<meta name="format-detection" content="telephone=no">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">      
<meta name="apple-mobile-web-app-title" content="IBKR">

<link rel="stylesheet" type="text/css" href="/css/bootstrap-4.3.1/bootstrap.min.css">

	<link rel="stylesheet" type="text/css" href="/css/bootstrap-4.3.1/bootstrap-migration.min.css">


<link rel="stylesheet" type="text/css" href="/css/fontawesome-6.2.0/all.min.css" media="screen">

<link rel="stylesheet" type="text/css" href="/css/fullcalendar-3.6.1/fullcalendar.css" media="screen">


<link rel="stylesheet" type="text/css" href="/lib/onebar/common.web.light.css?_=**********" data-theme="">


<link rel="stylesheet" type="text/css" href="/css/reg-am/am.min.css" media="screen">
<link rel="stylesheet" type="text/css" href="/css/reg-am/am-print.min.css" media="print">



    
    
    
    
    
    
        
    


<link id="amTheme" rel="stylesheet" type="text/css" href="/css/ibkr/theme-ibkr-portal.min.css" media="screen">



        


























 


    
    
    
        
        
        
                
    




	
        
    
    
     
    






























 


    
    
    
        
        
        
                
    




	
        
    
    
     
    


<script src="/scripts/common/js/jquery-3.4.1/jquery.min.js"></script>
<script src="/scripts/common/js/bootstrap-4.3.1/bootstrap.bundle.min.js"></script>
<script src="/scripts/common/js/angular.min.1.6.1.js"></script>
<script src="/scripts/common/js/angular-translate.2.11.0.js"></script>
<script src="/scripts/common/js/angular-translate-loader-partial.2.11.0.js"></script>


    
    
        <script src="/scripts/common/js/jquery.inputmask.bundle.min.js"></script>
    





    <script src="/scripts/common/js/angular-animate.min.1.5.2.js"></script>
    
    <script src="/scripts/common/js/highstock.5.0.12.js"></script>
    <script>
        
        var HighchartsLegacy = Highcharts;
        Highcharts = null;
    </script>
    <script src="/scripts/common/js/highcharts-7.2.1/highstock.js"></script>
    <script src="/scripts/common/js/highcharts-7.2.1/highcharts-more.js"></script>
    <script src="/scripts/common/js/highcharts-7.2.1/modules/timeline.js"></script>
    <script src="/scripts/common/js/highcharts-7.2.1/modules/treemap.js"></script>
    <script src="/scripts/common/js/highcharts-7.2.1/modules/drilldown.js"></script>

    <script>
        
        var HighchartsAM = Highcharts;
        Highcharts = null;
    </script>
    <script src="/scripts/common/js/am/jquery-ui.1.12.1.min.js"></script>
    <script src="/scripts/common/js/2015/moment.min.js"></script>
    <script src="/scripts/common/js/2015/fullcalendar.v3.6.1.js"></script>




<script type="text/javascript">
    

    
        var AM_LOGIN_LANGUAGE = 'en';
        var AM_SESSION_ID = 'BnIMpnmA0GE0AjT0hxTljpOGRJ59n5FcSzf3rGLCp5uaxuDpJl85Vl2OzgA5ffw3k7iHR9LIDiUK5RKR72kYdtZ4wVG64sq2T9DAOxVzRkpqPIO3XV7VYXWNMpbOCvHV78317y30DxbvxteeMCt2j9';
    
    
        var AM_INITIAL_PAGE = {"id":"Statements","icon":"","path":"/AccountManagement/template/page/reporting/reports/reporting.tabs.html","navigateAction":"NONE","skipFetch":false,"serverParams":{"action":"Statements"},"parent":"Reports","pageData":{},"subItems":[],"home":false};
    
    
    
        var AM_BRANCH = 'CORE';
    
    

    var APPLICATION_TITLE = 'Account Management';
    var LOGIN_APPLICATION = 'ACCOUNT_MANAGEMENT';

    var AM_CONTENT_HOST = 'www.interactivebrokers.co.uk';
    var AM_BAR_TYPE = 'ONE';

    var CRM_RACKSPACE_HOST = 'crm.interactivebrokers.com';

    var CACHE_FACTOR = '**********';

    var AM_APP_ROOT = '/AccountManagement';

    var USER_PLATFORM = 'WEB';
    var USER_PLATFORM_VERSION = 'prod';

    var APPLICATION_THEME = 'IBKR';
    var COLOR_THEME = 'LIGHT';

    var IS_WHITE_BRANDED = false;
    var WHITE_BRANDING_TITLE = 'IBKR';

    
    window.$app = window.$app || {};
    window.$app.option = window.$app.option || {};
    window.$app.option.theme = COLOR_THEME.toLowerCase();
    if (typeof AM_LOGIN_LANGUAGE !== 'undefined') {
        window.$app.option.language = AM_LOGIN_LANGUAGE;
    }

    

    
    
    
        var AM_LOADING_TEXT = 'Loading...';
    
    
    

    
</script>



    
    
        <script src="/am.webapp/ibmarketing.am.core.js?_=**********"></script>
        <script src="/AccountManagement/script/amCore.js?_=**********"></script>
    



<script type="text/javascript" src="/lib/onebar/common.web.js?_=**********"></script><style></style>
    
<script>(window.BOOMR_mq=window.BOOMR_mq||[]).push(["addVar",{"rua.upush":"true","rua.cpush":"false","rua.upre":"false","rua.cpre":"false","rua.uprl":"false","rua.cprl":"false","rua.cprf":"false","rua.trans":"SJ-b908fa7a-475f-4798-8036-49ffa99118b5","rua.cook":"true","rua.ims":"false","rua.ufprl":"true","rua.cfprl":"true","rua.isuxp":"false","rua.texp":"norulematch","rua.ceh":"false","rua.ueh":"false","rua.ieh.st":"0"}]);</script>
                              <script>!function(e){var n="https://s.go-mpulse.net/boomerang/";if("False"=="True")e.BOOMR_config=e.BOOMR_config||{},e.BOOMR_config.PageParams=e.BOOMR_config.PageParams||{},e.BOOMR_config.PageParams.pci=!0,n="https://s2.go-mpulse.net/boomerang/";if(window.BOOMR_API_key="E6798-MSRLA-AGTKP-4QTSS-AEXF9",function(){function e(){if(!o){var e=document.createElement("script");e.id="boomr-scr-as",e.src=window.BOOMR.url,e.async=!0,i.parentNode.appendChild(e),o=!0}}function t(e){o=!0;var n,t,a,r,d=document,O=window;if(window.BOOMR.snippetMethod=e?"if":"i",t=function(e,n){var t=d.createElement("script");t.id=n||"boomr-if-as",t.src=window.BOOMR.url,BOOMR_lstart=(new Date).getTime(),e=e||d.body,e.appendChild(t)},!window.addEventListener&&window.attachEvent&&navigator.userAgent.match(/MSIE [67]\./))return window.BOOMR.snippetMethod="s",void t(i.parentNode,"boomr-async");a=document.createElement("IFRAME"),a.src="about:blank",a.title="",a.role="presentation",a.loading="eager",r=(a.frameElement||a).style,r.width=0,r.height=0,r.border=0,r.display="none",i.parentNode.appendChild(a);try{O=a.contentWindow,d=O.document.open()}catch(_){n=document.domain,a.src="javascript:var d=document.open();d.domain='"+n+"';void(0);",O=a.contentWindow,d=O.document.open()}if(n)d._boomrl=function(){this.domain=n,t()},d.write("<bo"+"dy onload='document._boomrl();'>");else if(O._boomrl=function(){t()},O.addEventListener)O.addEventListener("load",O._boomrl,!1);else if(O.attachEvent)O.attachEvent("onload",O._boomrl);d.close()}function a(e){window.BOOMR_onload=e&&e.timeStamp||(new Date).getTime()}if(!window.BOOMR||!window.BOOMR.version&&!window.BOOMR.snippetExecuted){window.BOOMR=window.BOOMR||{},window.BOOMR.snippetStart=(new Date).getTime(),window.BOOMR.snippetExecuted=!0,window.BOOMR.snippetVersion=12,window.BOOMR.url=n+"E6798-MSRLA-AGTKP-4QTSS-AEXF9";var i=document.currentScript||document.getElementsByTagName("script")[0],o=!1,r=document.createElement("link");if(r.relList&&"function"==typeof r.relList.supports&&r.relList.supports("preload")&&"as"in r)window.BOOMR.snippetMethod="p",r.href=window.BOOMR.url,r.rel="preload",r.as="script",r.addEventListener("load",e),r.addEventListener("error",function(){t(!0)}),setTimeout(function(){if(!o)t(!0)},3e3),BOOMR_lstart=(new Date).getTime(),i.parentNode.appendChild(r);else t(!1);if(window.addEventListener)window.addEventListener("load",a,!1);else if(window.attachEvent)window.attachEvent("onload",a)}}(),"".length>0)if(e&&"performance"in e&&e.performance&&"function"==typeof e.performance.setResourceTimingBufferSize)e.performance.setResourceTimingBufferSize();!function(){if(BOOMR=e.BOOMR||{},BOOMR.plugins=BOOMR.plugins||{},!BOOMR.plugins.AK){var n="true"=="true"?1:0,t="cookiepresent",a="xqntdmdiovgguz77oxfq-f-96b2add25-clientnsv4-s.akamaihd.net",i="false"=="true"?2:1,o={"ak.v":"39","ak.cp":"1150933","ak.ai":parseInt("714965",10),"ak.ol":"0","ak.cr":16,"ak.ipv":4,"ak.proto":"h2","ak.rid":"7ef53af","ak.r":40072,"ak.a2":n,"ak.m":"a","ak.n":"essl","ak.bpcip":"***********","ak.cport":65088,"ak.gh":"**************","ak.quicv":"","ak.tlsv":"tls1.3","ak.0rtt":"","ak.0rtt.ed":"","ak.csrc":"-","ak.acc":"","ak.t":"1744795083","ak.ak":"hOBiQwZUYzCg5VSAfCLimQ==uNbrqf6u1uCrffbNmpykhgOzyb7m/jnmCBd7DDKwLscDLoLV3lEJ6rQnNlQoLZkmFR5MgOS81NgeV5teF3fCCo+KpzVAeCtzeo6TJidaAWFCKXkM2td2nzlLX9mGN57kM+nkvM0q/RRHLrX6B75CQAA1+ASc8+bUADw1R1j9HmPUy8Mr9ETKx8HfZmfcPGH5IfAi8WzwJufCbIbEziySgAmdP7cV9Ybs1cCKwN5Mac1M3iQG+465V8XOocLKkzz1xfXCZdnZm98OC/IyqOZBwdDl9vJ3XJOy2PxblmOKFAysNSma+U6t2XBT0W31cql2DW0XGhwDOQNHuqxQGkV/WilBk0mdB5NOC9yexuKeMlQp7dC8XnMKav5G5Fd5FCH7QRfE4g1t2YLp35J36hQaeSNXVRgRn/rB7+++KgvDgkA=","ak.pv":"67","ak.dpoabenc":"","ak.tf":i};if(""!==t)o["ak.ruds"]=t;var r={i:!1,av:function(n){var t="http.initiator";if(n&&(!n[t]||"spa_hard"===n[t]))o["ak.feo"]=void 0!==e.aFeoApplied?1:0,BOOMR.addVar(o)},rv:function(){var e=["ak.bpcip","ak.cport","ak.cr","ak.csrc","ak.gh","ak.ipv","ak.m","ak.n","ak.ol","ak.proto","ak.quicv","ak.tlsv","ak.0rtt","ak.0rtt.ed","ak.r","ak.acc","ak.t","ak.tf"];BOOMR.removeVar(e)}};BOOMR.plugins.AK={akVars:o,akDNSPreFetchDomain:a,init:function(){if(!r.i){var e=BOOMR.subscribe;e("before_beacon",r.av,null,null),e("onbeacon",r.rv,null,null),r.i=!0}return this},is_complete:function(){return!0}}}}()}(window);</script><link href="https://s.go-mpulse.net/boomerang/E6798-MSRLA-AGTKP-4QTSS-AEXF9" rel="preload" as="script"><script id="boomr-scr-as" src="https://s.go-mpulse.net/boomerang/E6798-MSRLA-AGTKP-4QTSS-AEXF9" async=""></script><style type="text/css">.paxos-badge svg{width:2.25em}.zh-badge svg{width:3.5em}.crypto-badge-align svg{vertical-align:-.25em}.crypto-provider-badge{display:inline-block}</style><style type="text/css">.order-ticket__search{position:relative}.order-ticket__search--results{border-radius:4px;max-height:20em;overflow-y:auto;position:absolute;z-index:8192}</style><style type="text/css">
.order-ticket__vdr-spinner {
    position: relative;
    background: white;
    width: 1em;
    height: 1em;
div {
        box-sizing: content-box;
}
}
.order-ticket__vdr-spinner--pie {
    width: 50%;
    height: 100%;
    transform-origin: 100% 50%;
    position: absolute;
}
.order-ticket__vdr-spinner--spinner {
    border-radius: 100% 0 0 100% / 50% 0 0 50%;
    border-right: none;
    animation: vdr-rota 30s linear forwards;
}
.order-ticket__vdr-spinner--filler {
    border-radius: 1% 100% 100% 1% / 1% 50% 50% 1%;
    left: 50%;
    opacity: 0;
    z-index: 100;
    animation: vdr-opa 30s steps(1, end) forwards reverse;
    border-left: none;
}
.order-ticket__vdr-spinner--mask {
    width: 50%;
    height: 100%;
    position: absolute;
    background: inherit;
    opacity: 1;
    animation: vdr-opa 30s steps(1, end) forwards;
}
@keyframes vdr-rota {
0% {
      transform: rotate(0deg);
}
100% {
      transform: rotate(360deg);
}
}
@keyframes vdr-opa {
0% {
      opacity: 1;
}
50%, 100% {
      opacity: 0;
}
}

</style><style type="text/css">#qty-chart svg.highcharts-root{height:90%;width:90%}</style><style type="text/css"></style><style type="text/css">.quote-icon-row{align-items:flex-start;display:flex}.quote-icon-img{flex:0 0 64px;padding:3px 0;width:64px}.quote-icon-img>*>svg,.quote-icon-img>svg,.quote-icon-img img{height:32px;vertical-align:middle;width:32px}.quote-icon-exp{flex:1 1 auto}</style><style type="text/css">.quoteIcons-icons{align-items:baseline;align-self:flex-end;display:flex;justify-content:flex-end;margin:0 -2px}.quoteIcons-icons-btn{border-radius:1px;height:14px;line-height:14px;margin:4px 2px;padding:0;width:14px}.quoteIcons-icons-btn img,.quoteIcons-icons-btn svg{height:100%;left:0;position:absolute;top:0;width:100%}.quoteIcons-icons-sm{justify-content:flex-start;width:100%}.quoteIcons-icons-sm .quote-icons-btn{height:10px;line-height:10px;width:10px}.quoteIcons-details-btn{margin:0;margin-inline-start:-4px;padding:0 4px}</style><style type="text/css">.bid-ask-yield.gap-xs{gap:.8em}.bid-ask-yield--max-content{width:max-content}.order-ticket__last-price-info{flex-wrap:wrap}.order-ticket__price-info--snapshot{align-items:center}.order-ticket__price-info .fgap-1{gap:1rem}.order-ticket__icons{align-self:flex-end}</style><style type="text/css">.trbanner-header{display:flex;justify-content:space-between}</style><style type="text/css">.mid-price-img{float:right;min-width:300px;width:50%}</style><style type="text/css">.eu-cost-modal ._mdef{overflow-y:hidden!important}</style><style type="text/css">.feedbackApp__popover{align-items:stretch;display:flex}</style><style type="text/css">.userVoiceSuggestion__field{overflow:hidden;padding:0!important;padding-inline-start:16px!important}.userVoiceSuggestion__field--endBtn{border:transparent;border-radius:0!important;height:100%}@media (max-width:380px){.userVoiceSuggestion__field label{font-size:14px;white-space:normal!important}}.userVoiceSuggestion__close svg{transform:rotate(45deg)}</style><style type="text/css">.parent-stylesheet-override{color:inherit!important;font-weight:inherit!important}.userVoiceItem__reverse{flex-direction:row-reverse}.userVoiceItem__btnGroup :first-child{border-bottom-right-radius:0;border-top-right-radius:0}.userVoiceItem__btnGroup :last-child{border-bottom-left-radius:0;border-top-left-radius:0}.userVoiceItem__item strong{font-weight:inherit!important}.userVoiceItem__item--light strong{background:#f0f4fa}.userVoiceItem__item--light-web strong{background:silver!important}.userVoiceItem__item--dark strong{background:#444}.userVoiceItem__vote--cell{align-items:center;display:flex;flex-direction:column;justify-content:center;min-height:32px;width:72px}.userVoiceItem__vote--btn{border-radius:0;min-height:32px;width:72px}.userVoiceItem__clampBtn,.userVoiceItem__clamp P{display:inline}.text-semibold p{font-weight:inherit}.fg70 p{color:inherit}</style><style type="text/css">.parent-stylesheet-override{color:inherit!important;font-weight:inherit!important}.uv_disclaimer{text-align:start}.uv_disclaimer__footer{text-align:end}</style><style type="text/css">.parent-stylesheet-override{color:inherit!important;font-weight:inherit!important}.userVoiceList ._ovfm{padding:0}</style><style type="text/css">.userVoiceNew__textarea ._fldi{white-space:normal}.userVoiceNew__col{flex-direction:column}</style><style type="text/css">.tws-skeleton ._btn,.tws-skeleton a,.tws-skeleton h1,.tws-skeleton h2,.tws-skeleton h3,.tws-skeleton h4,.tws-skeleton h5,.tws-skeleton img,.tws-skeleton p,.tws-skeleton span,.tws-skeleton svg{animation-duration:2.25s;animation-fill-mode:forwards;animation-iteration-count:infinite;animation-name:intAnimSkel;animation-timing-function:linear;background:linear-gradient(to right,var(--bg-canvas) 8%,var(--bg) 18%,var(--bg-canvas) 33%);background-size:800px 104px;border-radius:5px;color:transparent!important;position:relative}.tws-skeleton circle,.tws-skeleton path{opacity:0}.tws-skeleton .userVoiceItem__vote{border:none}.tws-skeleton .userVoiceItem__vote button{box-shadow:none}.tws-skeleton ._taba{border-bottom:none}@keyframes intAnimSkel{0%{background-position:-468px 0}to{background-position:468px 0}}.userVoice__form--btmPad{padding-bottom:16px}.userVoice ._btn{box-shadow:none!important}.routeFade-enter-active,.routeFade-leave-active{transition:opacity .35s ease-in-out}.routeFade-enter-from,.routeFade-leave-to{opacity:0}</style><style type="text/css">.parent-stylesheet-override{color:inherit!important;font-weight:inherit!important}.feedbackAppGeneralFB__btnContainer{display:grid;gap:8px;grid-template-columns:repeat(auto-fit,minmax(125px,2fr))}button.feedbackAppGeneralFB__btn{align-items:center;display:flex;flex-direction:column;min-height:125px;white-space:normal}button.feedbackAppGeneralFB__ngButton{flex-direction:row;min-height:auto!important;text-align:start;white-space:normal;width:100%}</style><style type="text/css">.feedbackAppEffort__selection{opacity:0}.feedbackAppEffort__selection--show{opacity:1}.feedbackAppEffort__grid{gap:8px}</style><style type="text/css">.feedbackAppRr{align-items:center;display:flex;flex-direction:column;justify-content:space-between}.feedbackAppRr button{width:100%}.feedbackAppRr i svg{height:auto!important;max-width:100%;width:auto!important}.feedbackAppRr__ui{transition:opacity .25s ease-in-out}.feedbackAppRr__ui--loading{opacity:0}</style><style type="text/css">.feedbackState{display:flex;flex-direction:column;height:100%}.feedbackState__ico{display:flex;justify-content:center}</style><style type="text/css">.feedbackAppFull__btm--wide button{padding:12px 24px;width:100%}</style><style type="text/css">.w100{width:100%}.h100{height:100%}.w25{width:25%}.feedback-fade-enter,.feedback-fade-fast,.feedback-fade-fast-leave-to,.feedback-fade-leave-to{opacity:0}.feedback-fade-enter-active,.feedback-fade-leave-active{transition:opacity .5s}.feedback-fade-fast-enter-active,.feedback-fade-fast-leave-active{transition:opacity .15s}.feedbackApp__impact ._field._fo{border:transparent}.feedbackApp__ta{height:150px;margin:0!important;resize:none;width:100%}.feedbackApp__ico svg{height:auto;width:auto}</style><style type="text/css">.direct-routing__decoration{border-radius:.75em}</style><style type="text/css">.order-ticket__order-preview-sidebar table{border-collapse:collapse;width:100%}.order-ticket__order-preview-sidebar table tr td:not(:first-child){padding-inline-start:1em}</style><style type="text/css">.order-ticket__animated-confirmation{position:relative}.order-ticket__arrow,.order-ticket__circle-filled,.order-ticket__circle-loader{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}.order-ticket__circle-progress svg{animation:odr-sbm-loader-spin 3s ease .5s infinite;height:3.5em;width:3.5em}.order-ticket__arrow svg{height:2.5em;width:2.5em}.order-ticket__circle-filled svg{height:3.5em;width:3.5em}.order-ticket__circle-filled svg .circle{stroke:var(--bg-accent);fill-opacity:0;stroke-dasharray:1000;stroke-dashoffset:1000;animation:odr-sbm-stroke 2s linear forwards}.order-ticket__circle-loader{animation:odr-sbm-fadeCircle 1s ease;border-radius:50%;height:3.5em;width:3.5em;will-change:opacity}.order-ticket__checkmark{animation:odr-sbm-checkmark 2s linear;border-right:4px solid var(--fg);border-top:4px solid var(--fg);height:25px;left:calc(50% - 14px);position:absolute;top:54%;transform:scaleX(-1) rotate(135deg);transform-origin:left top;width:12.5px;will-change:border-width,width,height}@keyframes odr-sbm-loader-spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}@keyframes odr-sbm-stroke{to{stroke-dashoffset:0}}@keyframes odr-sbm-fadeCircle{0%{opacity:0}70%{opacity:0}to{opacity:1}}@keyframes odr-sbm-checkmark{0%{border-width:0;height:0;width:0}80%{border-width:0;height:0;width:0}90%{border-width:3px;height:0;width:12.5px}to{border-width:3px;height:25px;width:12.5px}}</style><style type="text/css">.cp-action-menu-trigger svg{transform:rotate(90deg)}.cp-action-menu-trigger-hide{opacity:0}.cp-action-menu-trigger-show,.cp-action-menu-trigger:hover{opacity:1!important}</style><style type="text/css">.order_ticket__submit-view{align-items:center;display:flex;flex-direction:column}.order_ticket__submit-view__compact-table,.order_ticket__submit-view__compact-table table{width:100%}.order_ticket__submit-view--close{width:50%}.order_ticket__submit-view table{border-collapse:collapse}.order_ticket__submit-view table tr td:first-child{padding-inline-end:6em}</style><style type="text/css">.attached-swap-order{display:grid;gap:1em;grid-auto-flow:dense;grid-template-columns:1fr 1fr}.attached-swap-order__full-width{grid-column:span 2}</style><style type="text/css">.cbbanner{display:flex}.cbbanner-icon{width:20px}.cbbanner-text h1{font-size:1rem}</style><style type="text/css">.overnightFeatureInfo{border-radius:5px;max-width:500px}.overnightFeatureInfo__progress{height:50px;width:100%}.overnightFeatureInfo-faqbutton{margin:4px 0!important}</style><style type="text/css">.order-ticket__commission-adjustment__controls{gap:1em}.order-ticket__commission-adjustment-attached{grid-column:1/3}.order-ticket__sidebar{display:flex;flex-direction:column}.order-ticket__sidebar--grid{display:grid;gap:1em;grid-template-columns:1fr 1fr}.order-ticket__sidebar--field{height:2.5em}.order-ticket__sidebar--moc{font-family:inherit;height:40px;width:100%}.order-ticket__sidebar__alloc_grp_feat{border-radius:4px;justify-content:center}.order-ticket__sidebar--sticky{bottom:0;position:sticky;z-index:1000}.order-ticket__sidebar--disclaimer>p{margin-bottom:8px}.order-ticket__sidebar--fractions-label{grid-column:span 2}.order-ticket__opaque{opacity:.55;pointer-events:none}.order-ticket__attach-orders hr{margin-top:42px}</style><style type="text/css">.order-ticket__option-selection{display:flex;gap:1em}.order-ticket__contracts-container{max-height:25vh;overflow-x:auto}.order-ticket__contracts-container .order-ticket__contracts-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(50%,1fr))}.order-ticket__contracts-container .order-ticket__contracts-grid ._btn{white-space:normal}</style><style type="text/css">.order-ticket__bond-contracts{max-height:154px;overflow-y:auto}</style><style type="text/css">.crypto-intro .crypto-intro-text{line-height:2em}.crypto-intro .crypto-graphic svg{height:auto;width:180px}.crypto-intro .crypto-graphic.small svg{height:auto;width:150px}</style><style type="text/css">.no-funds-icon svg,.no-permission-icon svg{height:110px;width:120px}.no-funds-icon.large-icon svg,.no-permission-icon.large-icon svg{height:119px;width:127px}</style><style type="text/css">.order-ticket__account-search-container{height:100%;overflow-y:auto}.order-ticket__icon svg{height:2em;vertical-align:-.65em;width:2em}</style><style type="text/css">.dyn-acct-search{position:relative;z-index:10}.dyn-acct-search__bar{border:1px solid silver!important;box-shadow:0 .1em .1em rgba(32,33,36,.28)!important;margin:2px 3px}.dyn-acct-search__close{align-items:center;display:flex}.dyn-acct-search__close svg{fill:#999}.dyn-acct-search__input{border:none!important}.dyn-acct-search__search-icon{align-items:center;display:flex}.dyn-acct-search__search-icon svg{padding-top:.125em}.dyn-acct-search__input-container{display:flex;flex-direction:row}.dyn-acct-search__selected-search-icon svg{padding-top:.125em}.dyn-acct-search__acct-icon svg{width:1.5em}.dyn-acct-search__list{box-shadow:0 16px 22px rgba(0,0,0,.04),0 2px 6px 1px rgba(0,0,0,.05),0 9px 22px 6px rgba(0,0,0,.05);max-height:10em;overflow-y:auto;position:absolute;width:100%;z-index:8190}</style><style type="text/css">.fa-groups__justify-content-end{justify-content:flex-end}.fa-groups__fa-ac-container{height:200px}.fa-groups__fa_ac-table{max-height:250px}.fa-groups__fa_ac-table-sm{width:84vw}.fa-groups__accounts-section{align-content:center;flex-wrap:wrap;height:100px;justify-content:center}._ddo li[aria-disabled=true]:nth-child(4){font-weight:700;opacity:1}._ddo li[aria-disabled=true]:hover{background:var(--bg-dialog)}</style><style type="text/css">.fa-alloc__group-modal{z-index:1049}.fa-alloc__group-modal__tb-container{width:100%}.fa-alloc__group-modal__content{height:auto;max-height:calc(100vh - 200px);overflow:auto}.fa-alloc__group-modal__footer{align-items:center;display:flex;flex-direction:row-reverse;flex-wrap:wrap;gap:10px;justify-content:space-between}.fa-alloc__group-modal .gap-10{gap:10px}</style><style type="text/css">.order-ticket__crypto-disconnect{border-radius:5%}</style><style type="text/css">.order-ticket__sidebar{height:100%}.order-ticket__sidebar--container{display:flex;flex-direction:column;height:100%;overflow-y:auto}.order-ticket__sidebar--expanded{flex:1}.order-ticket__vdr-blur ._app>footer,.order-ticket__vdr-blur ._app>header,.order-ticket__vdr-blur ._app>main{-webkit-filter:blur(8px);filter:blur(8px);pointer-events:none}</style><style type="text/css">.order-ticket__account-portfolio{gap:2em;justify-content:space-between}</style><style type="text/css">.order-ticket__bar{align-items:center;justify-content:space-between}</style><style type="text/css">.align-center{align-items:center}.symbol-width{width:10%}</style><style type="text/css">.order-ticket__order-preview-modal{z-index:1040}.order-ticket__order-preview-modal table{border-collapse:collapse;width:100%}.order-ticket__order-preview-modal table tr td:not(:first-child){padding-inline-start:1em}.order-ticket__order-preview-modal table.w-40 tr td:first-child{width:40%}.order-ticket__order-preview-modal--allocations{max-height:10em;overflow-y:auto}</style><style type="text/css">.order-ticket-aot__container{flex-wrap:wrap;row-gap:1em}.order-ticket-aot__container--moc{font-family:inherit}.order-ticket-aot__footer{display:flex;justify-content:space-between}</style><style type="text/css">.fa-portal__barchart__skeleton-col,.fa-portal__skeleton-col{animation:pulse 1.5s ease-in-out infinite;background-color:var(--bg-alt);display:block;height:15px}.fa-portal__barchart__skeleton-col:first-child{height:10px;margin-right:8px}.fa-portal__barchart__skeleton-col:last-child{height:15px}.fa-portal__skeleton-pie-chart__container{align-items:center;background-color:var(--bg);display:flex;justify-content:center;min-height:inherit;overflow:hidden;padding:20px;position:relative}.fa-portal__skeleton-pie-chart__wrapper{align-items:center;display:flex;gap:16px}.fa-portal__skeleton-pie-chart__pie{animation:pulse 1.5s ease-in-out infinite;background:conic-gradient(var(--bg-alt) 0 10%,var(--bg-canvas) 10% 25%,var(--bg-alt) 25% 40%,var(--bg-canvas) 40% 50%,var(--bg-alt) 50% 60%,var(--bg-canvas) 60% 70%,var(--bg-alt) 70% 85%,var(--bg-canvas) 85% 95%,var(--bg-alt) 95% 100%);border-radius:50%;height:160px;position:relative;width:160px}@media (min-width:768px){.fa-portal__skeleton-pie-chart__pie{height:375px;width:375px}}.fa-portal__skeleton-pie-chart__pie:after{background-color:var(--bg);border-radius:50%;content:"";height:50%;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:50%}@keyframes pulse{0%,to{opacity:.6}50%{opacity:1}}.fa-portal .gap-4{gap:4px}.fa-portal .gap-8{gap:8px}.fa-portal .gap-16{gap:16px}.fa-portal .gap-24{gap:24px}.fa-portal .gap-32{gap:32px}.fa-portal .justify-start{justify-content:flex-start}.fa-portal .justify-end{justify-content:flex-end}.fa-portal .justify-space-between{justify-content:space-between}.fa-portal .flex-1{flex:1}.fa-portal .active-sort-field{color:var(--bg-primary)}@media print{.fa-portal .no-print{display:none}}.fa-portal .paxos-badge svg{width:2.25em}.fa-portal .flex-col-reverse{flex-direction:column-reverse}.fa-portal .self-start{align-self:flex-start}.fa-portal .self-end{align-self:flex-end}.fa-portal .self-center{align-self:center}.fa-portal .items-center{align-items:center}.fa-portal .items-end{align-items:flex-end}</style><style type="text/css">.fa-portal__barchart__skeleton-col,.fa-portal__skeleton-col{animation:pulse 1.5s ease-in-out infinite;background-color:var(--bg-alt);display:block;height:15px}.fa-portal__barchart__skeleton-col:first-child{height:10px;margin-right:8px}.fa-portal__barchart__skeleton-col:last-child{height:15px}.fa-portal__skeleton-pie-chart__container{align-items:center;background-color:var(--bg);display:flex;justify-content:center;min-height:inherit;overflow:hidden;padding:20px;position:relative}.fa-portal__skeleton-pie-chart__wrapper{align-items:center;display:flex;gap:16px}.fa-portal__skeleton-pie-chart__pie{animation:pulse 1.5s ease-in-out infinite;background:conic-gradient(var(--bg-alt) 0 10%,var(--bg-canvas) 10% 25%,var(--bg-alt) 25% 40%,var(--bg-canvas) 40% 50%,var(--bg-alt) 50% 60%,var(--bg-canvas) 60% 70%,var(--bg-alt) 70% 85%,var(--bg-canvas) 85% 95%,var(--bg-alt) 95% 100%);border-radius:50%;height:160px;position:relative;width:160px}@media (min-width:768px){.fa-portal__skeleton-pie-chart__pie{height:375px;width:375px}}.fa-portal__skeleton-pie-chart__pie:after{background-color:var(--bg);border-radius:50%;content:"";height:50%;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:50%}@keyframes pulse{0%,to{opacity:.6}50%{opacity:1}}.fa-portal .gap-4{gap:4px}.fa-portal .gap-8{gap:8px}.fa-portal .gap-16{gap:16px}.fa-portal .gap-24{gap:24px}.fa-portal .gap-32{gap:32px}.fa-portal .justify-start{justify-content:flex-start}.fa-portal .justify-end{justify-content:flex-end}.fa-portal .justify-space-between{justify-content:space-between}.fa-portal .flex-1{flex:1}.fa-portal .active-sort-field{color:var(--bg-primary)}@media print{.fa-portal .no-print{display:none}}.fa-portal .paxos-badge svg{width:2.25em}.fa-portal .flex-col-reverse{flex-direction:column-reverse}.fa-portal .self-start{align-self:flex-start}.fa-portal .self-end{align-self:flex-end}.fa-portal .self-center{align-self:center}.fa-portal .items-center{align-items:center}.fa-portal .items-end{align-items:flex-end}</style><style type="text/css">.fa-portal__notification-component__s svg{height:2em;width:2em}.fa-portal__notification-component__m svg{height:4em;width:4em}.fa-portal__notification-component__l svg{height:8em;width:8em}</style><style type="text/css">.fa-accordion{margin:0 auto;width:100%}.fa-accordion .slide-fade-enter-active,.fa-accordion .slide-fade-leave-active{max-height:1900px;overflow:hidden;transition:all .3s ease}.fa-accordion .slide-fade-enter,.fa-accordion .slide-fade-leave-to{max-height:0;opacity:0}</style><style type="text/css">.fa-accordion-header{align-items:center;background-color:var(--bg);cursor:pointer;display:flex;justify-content:space-between;padding:15px;transition:background-color .3s ease}.fa-accordion-header:hover{background-color:var(--bg-alt)}.fa-accordion-header[aria-disabled=true]{cursor:not-allowed;opacity:.6}.fa-accordion-left{flex-grow:1}.fa-accordion-right{align-items:center;display:flex;margin-inline-end:10px;margin-inline-start:10px}.fa-accordion-icon{align-items:center;display:flex;justify-content:center;transition:transform .3s ease}.fa-accordion-icon.is-active{transform:rotate(180deg)}.fa-accordion-icon svg{height:20px;width:20px}.fa-accordion-content{margin-top:2px;padding:16px}</style><style type="text/css">.fa-portal__barchart__skeleton-col,.fa-portal__skeleton-col{animation:pulse 1.5s ease-in-out infinite;background-color:var(--bg-alt);display:block;height:15px}.fa-portal__barchart__skeleton-col:first-child{height:10px;margin-right:8px}.fa-portal__barchart__skeleton-col:last-child{height:15px}.fa-portal__skeleton-pie-chart__container{align-items:center;background-color:var(--bg);display:flex;justify-content:center;min-height:inherit;overflow:hidden;padding:20px;position:relative}.fa-portal__skeleton-pie-chart__wrapper{align-items:center;display:flex;gap:16px}.fa-portal__skeleton-pie-chart__pie{animation:pulse 1.5s ease-in-out infinite;background:conic-gradient(var(--bg-alt) 0 10%,var(--bg-canvas) 10% 25%,var(--bg-alt) 25% 40%,var(--bg-canvas) 40% 50%,var(--bg-alt) 50% 60%,var(--bg-canvas) 60% 70%,var(--bg-alt) 70% 85%,var(--bg-canvas) 85% 95%,var(--bg-alt) 95% 100%);border-radius:50%;height:160px;position:relative;width:160px}@media (min-width:768px){.fa-portal__skeleton-pie-chart__pie{height:375px;width:375px}}.fa-portal__skeleton-pie-chart__pie:after{background-color:var(--bg);border-radius:50%;content:"";height:50%;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:50%}@keyframes pulse{0%,to{opacity:.6}50%{opacity:1}}.fa-portal .gap-4{gap:4px}.fa-portal .gap-8{gap:8px}.fa-portal .gap-16{gap:16px}.fa-portal .gap-24{gap:24px}.fa-portal .gap-32{gap:32px}.fa-portal .justify-start{justify-content:flex-start}.fa-portal .justify-end{justify-content:flex-end}.fa-portal .justify-space-between{justify-content:space-between}.fa-portal .flex-1{flex:1}.fa-portal .active-sort-field{color:var(--bg-primary)}@media print{.fa-portal .no-print{display:none}}.fa-portal .paxos-badge svg{width:2.25em}.fa-portal .flex-col-reverse{flex-direction:column-reverse}.fa-portal .self-start{align-self:flex-start}.fa-portal .self-end{align-self:flex-end}.fa-portal .self-center{align-self:center}.fa-portal .items-center{align-items:center}.fa-portal .items-end{align-items:flex-end}</style><style type="text/css">.fa-portal__inst-allocation-modal{z-index:1049}.fa-portal__inst-allocation-modal__tb-container{width:100%}.fa-portal__inst-allocation-modal__content{height:auto;max-height:calc(90vh - 250px);overflow:auto}.fa-portal__inst-allocation-modal__footer{align-items:center;display:flex;flex-direction:row-reverse;flex-wrap:wrap;gap:10px;justify-content:space-between}.fa-portal__inst-allocation-modal .gap-10{gap:10px}</style><style type="text/css">.fa-portal__dropdown-container{display:inline-block;position:relative}.fa-portal__dropdown-menu{background-color:var(--bg-dialog);border:1px solid var(--bg-gray);box-shadow:0 2px 5px rgba(0,0,0,.1);left:4px;position:absolute;top:96%;width:180px;z-index:1000}.fa-portal__dropdown-menu--compact{left:auto;right:4px;top:125%}.fa-portal__dropdown-menu__btn{align-items:center;background-color:var(--bg);border:1px solid var(--bg-gray);border-radius:4px;color:var(--bg-gray);cursor:pointer;display:flex;font-size:16px;justify-content:space-between;padding:10px 15px;width:100%}.fa-portal__dropdown-menu__btn .arrow{border-left:6px solid transparent;border-right:6px solid transparent;border-top:6px solid var(--fg-gray);transition:transform .3s ease-in-out}.fa-portal__dropdown-menu__btn.active,.fa-portal__dropdown-menu__btn:hover{color:var(--fg)}.fa-portal__dropdown-menu__btn.active .arrow,.fa-portal__dropdown-menu__btn:hover .arrow{border-top-color:var(--bg-accent)}.fa-portal__dropdown-section{border-bottom:1px solid var(--bg-gray);padding:10px}.fa-portal__dropdown-section:last-child{border-bottom:none}.fa-portal__dropdown-section__items{list-style-type:none;margin:0;padding:0}.fa-portal__dropdown-item{cursor:pointer;margin:0 -10px;padding:3px 10px}.fa-portal__dropdown-item:hover{background-color:rgba(48,126,242,.15)}.fa-portal__compact-menu svg{transform:rotate(90deg)}</style><style type="text/css">.fa-portal-error-log-code{background:#222;margin:0 auto;max-width:max-content}</style><style type="text/css">.fa-portal-error-log-code{background:var(--bg-canvas);border-radius:10px;margin:0 auto;max-width:max-content}</style><style type="text/css">.fa-portal__barchart__skeleton-col,.fa-portal__skeleton-col{animation:pulse 1.5s ease-in-out infinite;background-color:var(--bg-alt);display:block;height:15px}.fa-portal__barchart__skeleton-col:first-child{height:10px;margin-right:8px}.fa-portal__barchart__skeleton-col:last-child{height:15px}.fa-portal__skeleton-pie-chart__container{align-items:center;background-color:var(--bg);display:flex;justify-content:center;min-height:inherit;overflow:hidden;padding:20px;position:relative}.fa-portal__skeleton-pie-chart__wrapper{align-items:center;display:flex;gap:16px}.fa-portal__skeleton-pie-chart__pie{animation:pulse 1.5s ease-in-out infinite;background:conic-gradient(var(--bg-alt) 0 10%,var(--bg-canvas) 10% 25%,var(--bg-alt) 25% 40%,var(--bg-canvas) 40% 50%,var(--bg-alt) 50% 60%,var(--bg-canvas) 60% 70%,var(--bg-alt) 70% 85%,var(--bg-canvas) 85% 95%,var(--bg-alt) 95% 100%);border-radius:50%;height:160px;position:relative;width:160px}@media (min-width:768px){.fa-portal__skeleton-pie-chart__pie{height:375px;width:375px}}.fa-portal__skeleton-pie-chart__pie:after{background-color:var(--bg);border-radius:50%;content:"";height:50%;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:50%}@keyframes pulse{0%,to{opacity:.6}50%{opacity:1}}</style><style type="text/css">.no-models__text-section{width:55%}.no-models__video-section{width:45%}</style><style type="text/css">.fa-groups__group-list__justify-center{justify-content:center}</style><style type="text/css">.fa-groups__container{display:flex;gap:32px;margin:0 auto;max-width:1368px;padding:16px 0}.fa-groups__container--large{flex-direction:row}.fa-groups__container--medium{flex-direction:column-reverse}.fa-groups__sidebar{flex:1}.fa-groups__sidebar--sticky{align-self:start;height:fit-content;position:sticky;top:120px;width:100%}.fa-groups__main{flex:3}</style><style type="text/css">.fa-entity-selector__accounts-modal{z-index:1049}.fa-entity-selector__accounts-modal__tb-container{width:100%}.fa-entity-selector__accounts-modal__content{height:auto;max-height:calc(90vh - 250px);overflow:auto}.fa-entity-selector__accounts-modal__footer{align-items:center;display:flex;flex-direction:row-reverse;flex-wrap:wrap;gap:10px;justify-content:space-between}.fa-entity-selector__accounts-modal .gap-10{gap:10px}</style><style type="text/css">.fa-entity-selector__header--fixed{position:sticky;top:0;z-index:3}.fa-entity-selector__footer--fixed{bottom:0;position:sticky;z-index:3}</style><style type="text/css">.fa-portal .gap-4{gap:4px}.fa-portal .gap-8{gap:8px}.fa-portal .gap-16{gap:16px}.fa-portal .gap-24{gap:24px}.fa-portal .gap-32{gap:32px}.fa-portal .justify-start{justify-content:flex-start}.fa-portal .justify-end{justify-content:flex-end}.fa-portal .justify-space-between{justify-content:space-between}.fa-portal .flex-1{flex:1}.fa-portal .active-sort-field{color:var(--bg-primary)}@media print{.fa-portal .no-print{display:none}}.fa-portal .paxos-badge svg{width:2.25em}.fa-portal .flex-col-reverse{flex-direction:column-reverse}.fa-portal .self-start{align-self:flex-start}.fa-portal .self-end{align-self:flex-end}.fa-portal .self-center{align-self:center}.fa-portal .items-center{align-items:center}.fa-portal .items-end{align-items:flex-end}.fa-portal__barchart__skeleton-col,.fa-portal__skeleton-col{animation:pulse 1.5s ease-in-out infinite;background-color:var(--bg-alt);display:block;height:15px}.fa-portal__barchart__skeleton-col:first-child{height:10px;margin-right:8px}.fa-portal__barchart__skeleton-col:last-child{height:15px}.fa-portal__skeleton-pie-chart__container{align-items:center;background-color:var(--bg);display:flex;justify-content:center;min-height:inherit;overflow:hidden;padding:20px;position:relative}.fa-portal__skeleton-pie-chart__wrapper{align-items:center;display:flex;gap:16px}.fa-portal__skeleton-pie-chart__pie{animation:pulse 1.5s ease-in-out infinite;background:conic-gradient(var(--bg-alt) 0 10%,var(--bg-canvas) 10% 25%,var(--bg-alt) 25% 40%,var(--bg-canvas) 40% 50%,var(--bg-alt) 50% 60%,var(--bg-canvas) 60% 70%,var(--bg-alt) 70% 85%,var(--bg-canvas) 85% 95%,var(--bg-alt) 95% 100%);border-radius:50%;height:160px;position:relative;width:160px}@media (min-width:768px){.fa-portal__skeleton-pie-chart__pie{height:375px;width:375px}}.fa-portal__skeleton-pie-chart__pie:after{background-color:var(--bg);border-radius:50%;content:"";height:50%;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:50%}@keyframes pulse{0%,to{opacity:.6}50%{opacity:1}}</style><style type="text/css">.order-ticket-aot__contract-info{align-items:center;align-self:center;display:flex;gap:2em}.order-ticket-aot__contract-details{justify-content:space-between;width:100%}.order-ticket-aot__contract-details__2col-list .order-ticket-aot__contracts-container{grid-template-columns:repeat(2,1fr)}.order-ticket-aot__contract-details__alloc_grp_feat{border-radius:4px;justify-content:center}.order-ticket-aot__order-action{list-style-type:none;padding:0}.order-ticket-aot__order-action li{float:left;height:2em;position:relative;width:5.25em}.order-ticket-aot__order-action input,.order-ticket-aot__order-action label{bottom:0;display:block;left:0;position:absolute;right:0;top:0}.order-ticket-aot__order-action input[type=radio]{opacity:.01}</style><style type="text/css">.w80{width:70%}</style><style type="text/css">.quote-chart{padding:6px 0;position:relative}.quote-chart__error{align-items:center;bottom:44px;display:flex;flex-direction:column;justify-content:center;left:0;position:absolute;right:0;top:0;z-index:1}.quote-chart__loading{opacity:0}.quote-chart__info{position:absolute;right:5em;top:1em}.quote-chart iframe{height:100%;width:100%}</style><style type="text/css">.order-ticket__tv-chart{height:25em}</style><style type="text/css">.order-ticket__position-metrics table{border-collapse:collapse;width:100%}</style><style type="text/css">
.aot-dropdownChoice[data-v-baf18100] {
  cursor: pointer;
  padding: 10px 1.7em 10px 1em;
  height: 40px;
}
ul[data-v-baf18100] {
  padding: 0;
}
li[data-v-baf18100] {
  padding: 4px 1.7em 4px 1em;
  height: 30px;
}
.aot-popOver[data-v-baf18100] {
  margin-top: 5px;
  min-width: 150px;
}
</style><style type="text/css">
.aot-grpDropdownBtn {
  --icon-size: 0.8em;
  border-top: 0;
  border-left: 0;
  border-right: 0;
  background: var(--bg);
  padding: 0;
  min-width: 150px;
  height: 30px;
}
.aot-grpDropdownBtnContent {
  display: flex;
  justify-content: space-between;
  min-width: 100px;
  padding: 5px;
  gap: 5px;
}
.aot-arrowIcon {
  display: flex;
  align-items: center;
}
.aot-arrowIcon svg{
  width: var(--icon-size);
  height: var(--icon-size);
  opacity: 0.17;
}
</style><style type="text/css">
.container[data-v-e4cc5f9e] {
  display: flex;
  flex-direction: row;
  border-radius: 4px;
  align-items: center;
  height: fit-content;
}
.option[data-v-e4cc5f9e] {
  margin: 2px 3px;
  padding: 4px 6px;
  cursor: pointer;
  border-radius: 4px;
}
</style><style type="text/css">
.aot-advancedMethodItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 5px 1rem;
}
.aot-advancedMethodItem span{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.aot-methodInput {
  width: 35%;
}
</style><style type="text/css">
.aot-allocMethodList {
  display: flex;
  flex-direction: column;
}
.aot-allocHr {
  padding-left: 50px;
}
.aot-commonMethodItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 1rem;
}
.aot-p0 {
 padding: 0;
}
.navBtnFw svg {
  transform: scale(-1,1);
}
.aot-advanced{
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
}
.aot-pctInput {
  width: 50px;
  height: 33px;
  border-radius: 4px;
}
</style><style type="text/css">
.aot-selectedMethodContainer{
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: center;
}
.aot-capFirst::first-letter {
  text-transform: capitalize;
}
</style><style type="text/css">.aot-vericalCenter{align-items:center;display:flex;flex-direction:row}.aot-horizontalCenter{display:flex;flex-direction:row;justify-content:center}.aot-flexWrap{flex-wrap:wrap}.aot-dropdown{border-left:0;border-right:0;border-top:0}.aot-inputField{border-radius:4px;height:33px;width:140px}.aot-vericalBottom{align-items:end;display:flex;flex-direction:row}.aot-verticalStart{align-items:start;display:flex;flex-direction:row;width:100%}.aot-m0{margin:0}</style><style type="text/css">
.aot-buyGreen {
  color: var(--bg-add)
}
.aot-sellRed {
  color: var(--bg-sell)
}
.aot-qtyInput {
  max-width: 90%
}
.aot-qtyWithWarn {
  max-width: calc(90% - 36px)
}
.aot-colHeader {
  display: flex;
  flex-direction: row;
}
.aot-colHeaderRight {
  justify-content: flex-end;
}
.aot-defaultCursor {
  cursor: default;
}
</style><style type="text/css">
.aot-addBtn {
  cursor: pointer;
  color: var(--bg-add)
}
</style><style type="text/css">
.aot-filterField {
  height: 40px;
  border-radius: 5px;
}
.aot-sliderContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2em;
}
.aot-errorBorder{
  border: 1px solid red !important;
}
</style><style type="text/css">
.aot-horizontalSpread {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.aot-pointer:hover {
  cursor: pointer;
}
.aot-allocationTableTitle {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}
.aot-allocationViewHeader{
  min-height: 40px;
}
</style><style type="text/css">.aot__notification-component__s svg{height:2em;width:2em}.aot__notification-component__m svg{height:4em;width:4em}.aot__notification-component__l svg{height:8em;width:8em}</style><style type="text/css">.order-ticket__container{flex-wrap:wrap;row-gap:1em}.order-ticket__container--moc{font-family:inherit}.order-ticket__commission-adjustment__pt,.order-ticket__commission-adjustment__sl{flex-basis:100%}.order-ticket__profit-taker,.order-ticket__stop-loss{flex-wrap:wrap}.order-ticket__disclaimer>p{margin-bottom:16px}.order-ticket__footer{display:flex;justify-content:space-between}.order-ticket__opaque{opacity:.55;pointer-events:none}.order-ticket__attach-orders hr{margin-top:42px}.order-ticket__order-action{list-style-type:none;padding:0}.order-ticket__order-action li{float:left;height:2em;position:relative;width:5.25em}.order-ticket__order-action input,.order-ticket__order-action label{bottom:0;display:block;left:0;position:absolute;right:0;top:0}.order-ticket__order-action input[type=radio]{opacity:.01}</style><style type="text/css">.order-ticket__contract-info{align-items:center;align-self:center;display:flex;gap:2em}.order-ticket__contract-details{justify-content:space-between;width:100%}.order-ticket__contract-details__alloc_grp_feat{border-radius:4px;justify-content:center}.order-ticket__contract-details.flex-row .grow{flex:1}</style><style type="text/css">.account-metrics table{width:100%}.account-metrics table tr{height:2em}.account-metrics table td:first-child{width:60%}</style><style type="text/css">.cash-qty-disclaimer-text{width:85%}</style><style type="text/css">.history-chart-controls .button-controls svg{height:1em;width:1em}.history-chart-controls .button-controls ._toggle{margin-bottom:0;margin-top:0}</style><style type="text/css">.ccy__baseCurrency-loading{margin-top:20px}.ccy__baseCurrency-line{margin-top:20px;position:relative}.ccy__baseCurrency-line:after{background:inherit;border-bottom:1px solid;border-right:1px solid;border-color:inherit;content:"";height:20px;left:40px;position:absolute;top:-9px;-moz-transform:rotate(45deg);-webkit-transform:rotate(45deg);transform:rotate(45deg);width:20px}.ccy__baseCurrency-line .baseCurrency-value{background:inherit;position:absolute;right:.8em;top:-9px}.ccy__subtext{width:60%}.ccy__subtext.hidden{visibility:hidden}.ccy__loading{align-items:center;display:flex;justify-content:center;width:100%}.ccy__resetBtn{justify-content:flex-end}.ccy__preview-table{overflow-x:hidden}.ccy__side-pane-chart .highcharts-container{max-width:390px}[dir=rtl] .ccy__baseCurrency-line:after{left:auto;right:40px}[dir=rtl] .ccy__baseCurrency-line .baseCurrency-value{left:.8em;right:auto}</style><style type="text/css">.close-currency-disclaimer .icon{margin:1em auto}.close-currency-disclaimer__currency-exchange-icon svg{height:101px;width:99px}</style><style type="text/css">.convert-currency-pane .warning-banner{margin:0 -.5em}.convert-currency-pane.h100{height:100%}.convert-currency-pane.light-scroll{-ms-overflow-style:-ms-autohiding-scrollbar!important;overflow-y:auto!important;padding:0 .5em}.convert-currency-pane .close{padding:0}.convert-currency-pane .submit .icon{margin:1em auto}.convert-currency-pane .submit h5{text-align:center;width:100%}.convert-currency-pane .submit svg{height:100px;width:100px}.convert-currency-pane .ccy-conv-submit-btn{width:50%}</style><style type="text/css">.qt-youraccount table{border-collapse:collapse;width:100%}.qt-youraccount td{padding:2px 0;vertical-align:baseline}</style><style type="text/css">.ccy-exchange-rate-cell{max-width:130px}</style><style type="text/css">
.close-position-button {
    justify-content: flex-end;
}
</style><style type="text/css">.order-ticket__tabs{margin-left:0;margin-right:1.5em}.order-ticket__low-width svg{height:unset;width:unset}</style><style type="text/css">.order-ticket__order-details-pane{height:100%}</style><style type="text/css">.mf-exchange-dlg .mf-exchange-swap-fund{max-width:250px;width:100%}.mf-exchange-dlg .mf-exchange-swap-fund .current-fund-box{min-height:116px;width:250px}.mf-exchange-dlg .mf-exchange-swap-fund.mob{max-width:100%}.mf-exchange-dlg .mf-exchange-swap-fund.mob .current-fund-box{width:100%}.mf-exchange-dlg .mf-exchange-swap-icon svg{height:32px;width:32px}.mf-exchange-dlg .search-fund-table{max-height:375px;overflow:auto}.mf-exchange-dlg .search-fund-table .th-border thead th{border-bottom:1px}.mf-exchange-dlg .searchresults_company{white-space:pre-line}#hsbc-mf-exchange ._dlgf{border-top-style:none}</style><style type="text/css">.legal-disclosure-link{align-self:flex-end}</style><style type="text/css">.b-pchart-ph{opacity:.5}.b-pchart svg{height:auto;width:auto}.b-pchart__fail{justify-content:center!important}</style><style type="text/css">.b-lgd{display:flex;flex-direction:row}.b-lgd-cmp{-webkit-margin-end:16px;margin-inline-end:16px}.b-lgd-bul{-webkit-margin-end:4px;border-radius:50%;display:inline-block;height:1em;margin-inline-end:4px;vertical-align:-.15em;width:1em}</style><style type="text/css">.b-sbar{display:flex;flex-direction:column}.b-sbar-bar{display:flex;flex:0 0 12px;flex-direction:row;height:12px}.b-sbar-bar:not(:last-child){margin:0 0 12px}.b-sbar-cmp{background-color:currentColor;flex:0 0 auto;position:relative;transition:opacity .5s}.b-sbar-cmp:after{bottom:0;content:"";left:0;opacity:.5;position:absolute;right:0;top:0;transition:box-shadow .5s}.b-sbar._bsel .b-sbar-cmp{opacity:.5}.b-sbar._bsel .b-sbar-sel{opacity:1}.b-sbar-sel:after{box-shadow:0 5px 24px currentColor}.b-sbar._erounded .b-sbar-bar{border-radius:99em}.b-sbar._erounded .b-sbar-cmp:first-child{border-radius:99em 0 0 99em}.b-sbar._erounded .b-sbar-cmp:last-child{border-radius:0 99em 99em 0}.b-sbar._erounded .b-sbar-cmp:only-child{border-radius:99em}.b-sbar._erounded._rtl .b-sbar-cmp:first-child{border-radius:0 99em 99em 0}.b-sbar._erounded._rtl .b-sbar-cmp:last-child{border-radius:99em 0 0 99em}.b-sbar._aend{align-items:flex-end}</style><style type="text/css">.b-skel-loading .b-clgo,.b-skel-loading.b-effect-icon,.b-skel-loading.b-impico,.b-skel-loading button,.b-skel-loading p,.b-skel-loading span,.b-skel-loading svg{animation-duration:2.25s;animation-fill-mode:forwards;animation-iteration-count:infinite;animation-name:intAnimSkel;animation-timing-function:linear;background:linear-gradient(to right,var(--bg-canvas) 8%,var(--bg) 18%,var(--bg-canvas) 33%);background-size:800px 104px;border-radius:5px;color:transparent!important;position:relative}.b-skel-loading.b-effect-icon,.b-skel-loading.b-impico,.b-skel-loading span{border-radius:100%}.b-skel-loading.b-effect-icon svg,.b-skel-loading.b-impico>svg{opacity:0}.b-skel-loading.b-clgo{border-radius:15px!important}.b-skel-loading.b-clgo svg{opacity:0}.b-skel-loading span>h5{color:transparent!important}@keyframes lbAnimSkel{0%{background-position:-468px 0}to{background-position:468px 0}}.b-clgo{border:8px solid transparent;border-radius:4px}.b-clgo svg{height:100%;width:100%}.b-clgo-new svg{height:100%!important;width:100%!important}.b-clgo-new__logo-container{align-items:center;display:flex;justify-content:center}.img{height:100%;opacity:0;transition:.2 ease;user-select:none;width:100%}.img__loaded{opacity:1}</style><style type="text/css">.bg-impact-high{background-color:#1faf77}.bg-impact-medium{background-color:#98c42f}.bg-impact-low{background-color:#f2cd57}.fg-impact-high{color:#1faf77}.fg-impact-medium{color:#98c42f}.fg-impact-low{color:#f2cd57}.border-impact-high{border-color:#1faf77!important}.border-impact-medium{border-color:#98c42f!important}.border-impact-low{border-color:#f2cd57!important}.b-card{background-color:inherit;border-radius:14px;display:inline-flex;flex-direction:column;height:100%;position:relative}.b-card__content{height:100%;z-index:10}.b-card__grow{display:flex}.b-card__border,.b-card__highlight{border-radius:14px;height:100%;position:absolute;width:100%}.b-card__highlight{border-width:3px!important}.b-card__footer{align-self:flex-end;width:100%}</style><style type="text/css">.b-csym{align-items:center;display:flex;flex-direction:column;height:100%;padding-bottom:16px!important;position:relative;width:100%}.b-csym__phLogo svg{fill:none;height:auto;width:auto}.b-csym__arrow{align-self:flex-end}.b-csym__arrow svg{height:18px;width:auto}.b-csym__arrow--hidden{opacity:0!important}.b-csym__frozen{color:inherit!important}.b-csym__lnClmp{-webkit-box-orient:vertical;-webkit-line-clamp:2;line-clamp:2;display:-webkit-box;max-width:95%;overflow:hidden}.b-csym__lnClmp p{hyphens:auto;text-overflow:ellipsis;word-break:break-word}</style><style type="text/css">.b-skel-loading .b-clgo,.b-skel-loading.b-effect-icon,.b-skel-loading.b-impico,.b-skel-loading button,.b-skel-loading p,.b-skel-loading span,.b-skel-loading svg{animation-duration:2.25s;animation-fill-mode:forwards;animation-iteration-count:infinite;animation-name:intAnimSkel;animation-timing-function:linear;background:linear-gradient(to right,var(--bg-canvas) 8%,var(--bg) 18%,var(--bg-canvas) 33%);background-size:800px 104px;border-radius:5px;color:transparent!important;position:relative}.b-skel-loading.b-effect-icon,.b-skel-loading.b-impico,.b-skel-loading span{border-radius:100%}.b-skel-loading.b-effect-icon svg,.b-skel-loading.b-impico>svg{opacity:0}.b-skel-loading.b-clgo{border-radius:15px!important}.b-skel-loading.b-clgo svg{opacity:0}.b-skel-loading span>h5{color:transparent!important}@keyframes lbAnimSkel{0%{background-position:-468px 0}to{background-position:468px 0}}.bg-impact-high{background-color:#1faf77}.bg-impact-medium{background-color:#98c42f}.bg-impact-low{background-color:#f2cd57}.fg-impact-high{color:#1faf77}.fg-impact-medium{color:#98c42f}.fg-impact-low{color:#f2cd57}.border-impact-high{border-color:#1faf77!important}.border-impact-medium{border-color:#98c42f!important}.border-impact-low{border-color:#f2cd57!important}.b-impico svg{height:auto;width:auto}</style><style type="text/css">.b-effect-icon svg{height:auto;width:auto}.b-effect-icon__circle{border-radius:100%}.b-effect-icon__circle,.b-effect-icon__circle span{align-items:center;display:flex;justify-content:center}.b-effect-icon__circle span{height:100%;width:100%}.b-effect-icon__circle span svg{height:100%;width:auto}</style><style type="text/css">.bg-impact-high{background-color:#1faf77}.bg-impact-medium{background-color:#98c42f}.bg-impact-low{background-color:#f2cd57}.fg-impact-high{color:#1faf77}.fg-impact-medium{color:#98c42f}.fg-impact-low{color:#f2cd57}.border-impact-high{border-color:#1faf77!important}.border-impact-medium{border-color:#98c42f!important}.border-impact-low{border-color:#f2cd57!important}.b-impval{display:flex;flex-direction:column;height:100%;justify-content:flex-start;position:relative}.b-impval__outcome{border-width:3px!important}.b-impval__outcomeIcon{display:flex;flex-shrink:1;justify-content:space-between;width:100%}.b-impval__outcomeIcon .b-effect-icon{margin-bottom:auto}.b-impval__score{align-self:flex-start;border-radius:100%;flex-direction:column;height:32px;justify-content:center;min-width:32px;position:relative}.b-impval__score--letterGrade{color:#fff!important;line-height:32px!important;z-index:2}.b-impval__impact{border-radius:30px}.b-impval__btn{margin-left:auto;white-space:normal}p.b-impval__text{word-wrap:break-word;hyphens:auto;line-height:1.2em;white-space:normal}.b-impval__btm{display:flex;justify-content:flex-end;margin-top:auto!important}.b-impval__btm--setting{align-items:center;display:flex;justify-content:space-between;width:100%}</style><style type="text/css">.b-skel-loading .b-clgo,.b-skel-loading.b-effect-icon,.b-skel-loading.b-impico,.b-skel-loading button,.b-skel-loading p,.b-skel-loading span,.b-skel-loading svg{animation-duration:2.25s;animation-fill-mode:forwards;animation-iteration-count:infinite;animation-name:intAnimSkel;animation-timing-function:linear;background:linear-gradient(to right,var(--bg-canvas) 8%,var(--bg) 18%,var(--bg-canvas) 33%);background-size:800px 104px;border-radius:5px;color:transparent!important;position:relative}.b-skel-loading.b-effect-icon,.b-skel-loading.b-impico,.b-skel-loading span{border-radius:100%}.b-skel-loading.b-effect-icon svg,.b-skel-loading.b-impico>svg{opacity:0}.b-skel-loading.b-clgo{border-radius:15px!important}.b-skel-loading.b-clgo svg{opacity:0}.b-skel-loading span>h5{color:transparent!important}@keyframes lbAnimSkel{0%{background-position:-468px 0}to{background-position:468px 0}}.bg-impact-high{background-color:#1faf77}.bg-impact-medium{background-color:#98c42f}.bg-impact-low{background-color:#f2cd57}.fg-impact-high{color:#1faf77}.fg-impact-medium{color:#98c42f}.fg-impact-low{color:#f2cd57}.border-impact-high{border-color:#1faf77!important}.border-impact-medium{border-color:#98c42f!important}.border-impact-low{border-color:#f2cd57!important}.b-value-badge{border-radius:100%;position:relative}.b-value-badge,.b-value-badge__icon{align-items:center;display:flex;justify-content:center}.b-value-badge__icon svg{height:100%!important}.b-value-badge__score{align-self:flex-end;border-radius:100%;bottom:-5px;flex-direction:column;height:25px;justify-content:center;min-width:25px;position:absolute;right:0}.b-value-badge__score--letterGrade{color:#fff!important;line-height:25px!important;z-index:2}</style><style type="text/css">.b-goal-header__59{content:url(/lib/brokerage/59.jpg)}.b-goal-header__58{content:url(/lib/brokerage/58.jpg)}.b-goal-header__57{content:url(/lib/brokerage/57.jpg)}.b-goal-header__56{content:url(/lib/brokerage/56.jpg)}.b-goal-header__65{content:url(/lib/brokerage/65.jpg)}.b-goal-header__55{content:url(/lib/brokerage/55.jpg)}.b-goal-header__54{content:url(/lib/brokerage/54.jpg)}.b-goal-header__53{content:url(/lib/brokerage/53.jpg)}.b-goal-header__52{content:url(/lib/brokerage/52.jpg)}.b-goal-header__51{content:url(/lib/brokerage/51.jpg)}.b-goal-header__50{content:url(/lib/brokerage/50.jpg)}.b-goal-header__SUSTAINABLE_PRODUCT{content:url(/lib/brokerage/SUSTAINABLE_PRODUCT.jpg)}.b-goal-header__RACIAL_EQUALITY{content:url(/lib/brokerage/RACIAL_EQUALITY.jpg)}.b-goal-header__MINDFUL_BUSINESS_MODELS{content:url(/lib/brokerage/MINDFUL_BUSINESS_MODELS.jpg)}.b-goal-header__LGBTQ_INCLUSION{content:url(/lib/brokerage/LGBTQ_INCLUSION.jpg)}.b-goal-header__GENDER_EQUALITY{content:url(/lib/brokerage/GENDER_EQUALITY.jpg)}.b-goal-header__FAIR_LABOR{content:url(/lib/brokerage/FAIR_LABOR.jpg)}.b-goal-header__ETHICAL_LEADERSHIP{content:url(/lib/brokerage/ETHICAL_LEADERSHIP.jpg)}.b-goal-header__CONSUMER_TRANSPARENCY{content:url(/lib/brokerage/CONSUMER_TRANSPARENCY.jpg)}.b-goal-header__CUSTOMER_SAFETY{content:url(/lib/brokerage/CUSTOMER_SAFETY.jpg)}.b-goal-header__LAND_HEALTH{content:url(/lib/brokerage/LAND_HEALTH.jpg)}.b-goal-header__CLEAN_AIR{content:url(/lib/brokerage/CLEAN_AIR.jpg)}.b-goal-header__OCEAN_LIFE{content:url(/lib/brokerage/OCEAN_LIFE.jpg)}.b-goal-header__PURE_WATER{content:url(/lib/brokerage/PURE_WATER.jpg)}.b-goal-header__img{height:auto;max-height:425px;overflow:hidden}.b-goal-header__img img{display:block}.b-goal-header__img--content{height:auto;width:100%}.b-goal-header__overlap{margin-top:-44px;position:relative}.b-goal-header__preference{border-radius:25px}.b-goal-header__lineClamp{-webkit-box-orient:vertical;-webkit-line-clamp:3;line-clamp:3;display:-webkit-box;overflow:hidden}</style><style type="text/css">.b-chip{border-radius:20px;padding:3px}.b-chip__icon{align-items:center;border-radius:100%;display:flex;flex-direction:column;height:32px;justify-content:center;width:32px}.b-chip__icon svg{height:22px!important;width:22px}</style><style type="text/css">.bd-container{z-index:1000}.bd-container__dark .pane{box-shadow:0 4px 0 hsla(0,0%,100%,.1)}.bd-container .pane{background:var(--bg-dialog);border-radius:16px;color:var(--fg);max-width:none}</style><style type="text/css">.faq-categories__item{cursor:pointer}.faq-categories__item_active>span{font-weight:700}.faq-categories__item:hover>span{text-decoration:underline}</style><style type="text/css">.yt_player{height:0;padding-bottom:56.25%;position:relative;width:100%}.yt_player__iframe-wrapper{height:100%;left:0;position:absolute;top:0;width:100%}</style><style type="text/css">.ibot-faq__text img{margin:12px 0}.ibot-faq__text_hideButtons a[href*="tws://"]{display:none!important}.ibot-faq__text p:first-child>strong:only-child>a:only-child{border:1px solid;display:inline-block;margin-bottom:10px;padding:7px;text-decoration:none}.ibot-faq__actions{align-items:center;display:flex;justify-content:space-between}.ibot-faq__actionsCopy{cursor:pointer}.ibot-faq__actionsCopy:hover{transform:scale(1.1)}.ibot-faq__navigation{align-items:center;display:flex;flex-wrap:wrap}.ibot-faq__navigation-item button{word-wrap:break-word;text-align:start;white-space:normal}.ibot-faq__ibot{cursor:pointer}.ibot-faq__right-actions{align-items:center;display:flex;flex-wrap:nowrap}</style><style type="text/css">.faq-articles-list__item{cursor:pointer}.faq-articles-list__item-title{display:flex}.faq-articles-list__item-title:hover{text-decoration:underline}</style><style type="text/css">.icon-14{height:14px;width:auto}.ibot-forumAd{flex-wrap:wrap}</style><style type="text/css">.faq-articles__title{align-items:baseline;display:flex;justify-content:space-between}.faq-articles__copy{display:flex}.faq-articles__copyIcon{cursor:pointer}.faq-articles__copyIcon:hover{transform:scale(1.1)}.faq-articles__right-controls{align-items:center;display:flex;flex-shrink:0;justify-content:flex-end}.faq-articles__right-controls--dd span{margin-top:1.5px}</style><style type="text/css">.submit-field-wrapper{position:relative}.submit-field-wrapper .submit-field_active{border-bottom-left-radius:0;border-bottom-right-radius:0}.submit-field-wrapper .submit-field__results{border-top:0;box-shadow:0 5px 8px rgba(0,0,0,.2)}.submit-field-wrapper .submit-field-cp__results{box-shadow:unset}.submit-field{align-items:center;border-radius:6px;display:flex;justify-content:center;min-height:56px}.submit-field.submit-field-cp{box-shadow:unset}.submit-field:active{box-shadow:0 0 12px rgba(0,0,0,.2)}.submit-field__subject{border-radius:20px;border-width:0}.submit-field__loading{background:#ddd;height:2px;overflow:hidden;position:relative;top:-10px;width:100%}.submit-field__loading:before{animation:loading 2s linear infinite;background:#2980b9;content:"";height:2px;left:-200px;position:absolute;width:200px}.submit-field__submit-button{height:0;left:0;position:absolute;top:0;visibility:hidden;width:0}.submit-field__input-content{align-items:center;flex:1;padding:5px 0 5px 10px}.submit-field__input-content,.submit-field__suggestions{display:flex;flex-wrap:wrap;height:100%}.submit-field__inputField{background-color:transparent;border:none;flex:1;font-size:17px;height:100%;height:40px;outline:none}.submit-field__search-icon{margin-left:10px}.submit-field__search-icon_pointer{cursor:pointer}.submit-field__clear-icon{cursor:pointer;display:inline-block;margin-right:8px;transform:rotate(45deg)}.submit-field__clear-icon_btn{cursor:pointer;margin-left:4px;margin-right:0}.submit-field__results{height:min-content;left:0;position:absolute;top:100%;width:100%;z-index:1024}.submit-field__list-item{cursor:pointer}.slide-enter,slide-leave-to{opacity:0;transform:translateY(-20%)}.slide-enter-to{opacity:1;transform:translateY(0)}.slide-enter-active,.slide-leave-active{transition:all .2s ease-in}@keyframes loading{0%{left:-200px;width:30%}50%{width:30%}70%{width:70%}80%{width:50%}95%{width:120%}to{left:100%}}</style><style type="text/css">.bot3-feedback p{margin-top:2px}.bot3-feedback__viz svg{animation:fb-wiggle .3s forwards}.bot3-feedback ._btn.link.fg70:focus:not(:disabled){color:unset!important}.bot3-feedback ._btn:focus.fg70:after{color:unset!important;opacity:0!important}@keyframes fb-wiggle{0%{transform:rotate(0deg)}25%{transform:rotate(-10deg)}75%{transform:rotate(8deg)}to{transform:rotate(0deg)}}</style><style type="text/css">.ibot-faq__text img{margin:12px 0}.ibot-faq__text_hideButtons a[href*="tws://"]{display:none!important}.ibot-faq__text p:first-child>strong:only-child>a:only-child{border:1px solid;display:inline-block;margin-bottom:10px;padding:7px;text-decoration:none}.ibot-faq__actions{align-items:center;display:flex;justify-content:space-between}.ibot-faq__actionsCopy{cursor:pointer}.ibot-faq__actionsCopy:hover{transform:scale(1.1)}.ibot-faq__navigation{align-items:center;display:flex;flex-wrap:wrap}.ibot-faq__navigation-item button{word-wrap:break-word;text-align:start;white-space:normal}.ibot-faq__ibot{cursor:pointer}.ibot-faq__right-actions{align-items:center;display:flex;flex-wrap:nowrap}</style><style type="text/css">.faq-content__noResults{align-items:center;display:flex;flex-direction:column;justify-content:center}.faq-content__noResults-back{align-self:flex-start}.faq-content__error .fs4{display:none}.faq-content__error-content{align-items:center;display:flex;flex-direction:column;justify-content:center}.faq-content__question,.faq-content__similar-item{cursor:pointer}.faq-content__question:hover .faq-content__question-text,.faq-content__question:hover .faq-content__similar-item-text,.faq-content__similar-item:hover .faq-content__question-text,.faq-content__similar-item:hover .faq-content__similar-item-text{text-decoration:underline}.faq-content__card{border-radius:4px}.faq-content__notes table{border-collapse:collapse}.faq-content__notes table,.faq-content__notes td,.faq-content__notes th{border:1px solid var(--fg);padding:5px}.faq-content__notes ul{list-style-type:disc!important}.faq-content__notes ol{list-style-type:decimal!important}</style><style type="text/css">.bot3-skeleton__item,.bot3-skeleton__youtube,.bot3-skeleton h1,.bot3-skeleton h2,.bot3-skeleton h3,.bot3-skeleton h4,.bot3-skeleton h5,.bot3-skeleton li,.bot3-skeleton ol,.bot3-skeleton p,.bot3-skeleton ul{animation-duration:1.75s;animation-fill-mode:forwards;animation-iteration-count:infinite;animation-name:intAnimSkel;animation-timing-function:linear;background:linear-gradient(to right,var(--bg-canvas) 8%,var(--bg) 18%,var(--bg-canvas) 33%);background-size:1000px 104px;border-radius:1px;color:transparent!important;position:relative;width:100%}.bot3-skeleton__item--quarter,.bot3-skeleton__youtube--quarter,.bot3-skeleton h1--quarter,.bot3-skeleton h2--quarter,.bot3-skeleton h3--quarter,.bot3-skeleton h4--quarter,.bot3-skeleton h5--quarter,.bot3-skeleton li--quarter,.bot3-skeleton ol--quarter,.bot3-skeleton p--quarter,.bot3-skeleton ul--quarter{width:33%}.bot3-skeleton__noAnimate *{animation-name:null!important;background:inherit}.bot3-skeleton__youtube{height:400px;width:100%}.bot3-skeleton__item{height:25px;margin-bottom:7px}@keyframes intAnimSkel{0%{background-position:-468px 0}to{background-position:468px 0}}</style><style type="text/css">._ibot-ovfl{align-items:stretch;display:flex;flex-direction:row;flex-wrap:nowrap}._ibot-ovfl ._ovfe,._ibot-ovfl ._ovfs{align-items:stretch;display:flex;opacity:1;position:relative;width:0;z-index:1}._ibot-ovfl ._ovfs{justify-content:flex-start}._ibot-ovfl ._ovfe{justify-content:flex-end}._ibot-ovfl ._ovfbl,._ibot-ovfl ._ovfbr{flex:0 0 40px;padding:0 4px!important}._ibot-ovfl ._ovfm{align-self:baseline;flex:0 0 100%;height:100%;overflow-x:scroll;overflow-y:hidden;position:relative;width:10px;z-index:0}._ibot-ovfl ._ovfm::-webkit-scrollbar{display:none}._ibot-ovfl ._ovfch{overflow-x:hidden}._ibot-ovfl .ib-row>._ovfm{display:flex;flex-direction:row}._ibot-ovfl ._ovfto-enter,._ibot-ovfl ._ovfto-leave-to{opacity:0}._ibot-ovfl ._ovfto-enter-active,._ibot-ovfl ._ovfto-leave-active{transition:opacity .5s}</style><style type="text/css">.ibot3-source{display:flex;flex-direction:column;height:68px;justify-content:space-between;margin-left:0;min-width:280px;width:286px}.ibot3-sources{display:flex}.ibot3-source-title{-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box;line-height:1.3125;overflow:hidden;text-align:left;text-overflow:ellipsis;white-space:normal}.ibot3-source .icon-10{align-self:center;display:flex}.ibot3-source .icon-10 svg{height:10px;width:auto}</style><style type="text/css">._app.ibot-iframe>._dlg{align-items:flex-start;top:50px}.ibot3-stream{border-radius:8px;display:flex;flex-direction:column}.ibot3-stream-sales-eng{margin-right:-3px}.ibot3-stream__dialog{width:100%}.ibot3-stream__video{align-items:stretch;border-radius:10px;display:flex;max-width:350px;overflow:hidden;width:100%}.ibot3-stream__video p{white-space:normal}.ibot3-stream__video-title{display:flex;flex-direction:column;justify-content:space-between;max-height:125px}.ibot3-stream__video-title div:last-child{flex-basis:30%!important}.ibot3-stream__video-title-text{-webkit-line-clamp:4;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden;text-overflow:ellipsis}.ibot3-stream__video span{align-self:flex-end}.ibot3-stream__acBtn{white-space:inherit}.ibot3-stream__stream{max-height:168px;min-height:168px;overflow:hidden;position:relative;transition:all .15s cubic-bezier(1,1,1,1)}.ibot3-stream__stream--height{max-height:3000px;transition:all .15s cubic-bezier(1,1,1,1)}.ibot3-stream__stream article *{text-overflow:ellipsis}.ibot3-stream__stream article>ol{list-style-type:decimal;margin:.625em 0;padding-inline-start:2em}.ibot3-stream__btm{align-self:flex-start;justify-content:flex-end;margin-top:auto;width:100%}.ibot3-stream__arrow svg{transition:transform .25s ease-in-out}.ibot3-stream__arrow--flip svg{transform:rotate(180deg)}.ibot3-stream__logo{margin-top:2px}.ibot3-stream__logo svg{height:30px;width:auto}.ibot3-stream__loading{align-items:center;display:flex}.ibot3-stream__loading:after{animation:ellipsis 1.2s steps(4) infinite;content:"\2026";display:inline-block;overflow:hidden;vertical-align:bottom;width:0}.ibot3-stream__youtube{position:relative}.ibot3-stream__youtube-overlay{cursor:pointer;height:100%;left:0;position:absolute;top:0;width:100%;z-index:10000}.ibot3-stream__youtube iframe{border-radius:10px}.ibot3-stream__related{margin-left:36px}.ibot3-stream__related-compact{margin-left:0!important}.ibot3-stream__content{display:flex;justify-content:space-between}.ibot3-stream__content div:first-child{flex-basis:100%!important}.ibot3-stream__content div:last-child{flex-basis:60%}.ibot3-stream__content-related{margin-left:36px}.ibot3-stream__content-compact{display:flex;flex-direction:column-reverse;justify-content:space-between;margin-top:10px}.ibot3-stream__content-compact div:first-child{flex-basis:100%}.ibot3-stream__content-compact-related{margin-left:0!important}@keyframes cursor-blink{0%{opacity:0}}@keyframes ellipsis{to{width:1.25em}}</style><style type="text/css">.ibot3-response{border-radius:8px;display:flex;flex-direction:column;width:100%}.ibot3-response-feedbackPane{border-radius:5px;flex-shrink:0!important;height:100%;height:310px;margin-left:16px;max-width:230px!important}.ibot3-response__topFAQ{min-height:600px}.ibot3-response__piiViolation{border-radius:4px}.ibot3-response__mask{opacity:0;transition:opacity .25s ease-in-out}.ibot3-response__mask--show{opacity:1}.ibot3-response__articles button{white-space:normal}</style><style type="text/css">._app.ibot-iframe>._dlg{align-items:flex-start;top:50px}.bot3-disclaimer .icon-24 svg{height:24px;width:auto}.bot3-disclaimer__toast{border-radius:4px;overflow:hidden;position:relative}.bot3-disclaimer__toast--bar{height:100%;left:0;position:absolute;top:0;width:4px}</style><style type="text/css">.bot3-fail{border-radius:4px;overflow:hidden;position:relative}.bot3-fail__bar{height:100%;left:0;position:absolute;top:0;width:4px}</style><style type="text/css">.submit-field-results{border-radius:8px;overflow:hidden;position:relative}.submit-field-results button.link{white-space:normal}.submit-field-results.submit-field-results-cp{box-shadow:unset}.submit-field-results:active{box-shadow:0 0 12px rgba(0,0,0,.2)}.submit-field-results__subject{border-radius:20px;border-width:0}.submit-field-results__loading{background:#ddd;height:3px;overflow:hidden;position:absolute;top:0;width:100%}.submit-field-results__loading:before{animation:loading 2s linear infinite;background:#2980b9;content:"";height:2px;left:-200px;position:absolute;width:200px}.submit-field-results__list-item{cursor:pointer}@keyframes loading{0%{left:-200px;width:30%}50%{width:30%}70%{width:70%}80%{width:50%}95%{width:120%}to{left:100%}}</style><style type="text/css">.ibot-sales-eng{align-items:center;display:flex;flex-direction:column;justify-content:center}.ibot-sales-eng-review{border-radius:5px;min-width:500px;width:70%}.ibot-sales-eng-review__edit{border:0;padding:4px!important}.ibot-sales-eng-submit{display:flex;justify-content:end;width:100%}.ibot-sales-eng-table{align-items:center;border-radius:10px;display:flex;flex-wrap:wrap}.ibot-sales-eng-table p{flex-basis:150px;min-width:150px}.ibot-sales-eng-content{width:80%}.ibot-sales-eng__field{width:100%}.ibot-sales-eng__field ._field{border-radius:5px}.ibot-sales-eng__submit{align-items:flex-start!important;width:100%}.ibot-sales-eng__submit ._btn{margin-right:-3px}.ibot3__formBtn{border-radius:20px!important;border-width:0}.ibot3__formBtn svg{height:18px!important}.ibot3__close svg{transform:rotate(45deg)}.ibot3__tah{height:min-content;left:0;position:absolute;top:80px;width:100%;z-index:1024}.ibot3__fieldWrap{position:sticky;top:0;z-index:10}.ibot3__field{border-radius:6px;overflow:hidden;padding:0 0 0 16px!important}.ibot3__field svg{height:24px;width:auto}.ibot3__field--validate{animation:shakeValidate .2s ease-in-out 0s 2}.ibot3__field--endBtn{border:transparent;border-radius:0!important;height:100%}.ibot3__mask{opacity:0!important;transition:opacity .25s ease-in-out}.ibot3__mask--show{opacity:1!important}@keyframes shakeValidate{0%{margin-left:0}25%{margin-left:.35rem}50%{margin-left:.5rem}75%{margin-left:-.35rem}to{margin-left:0}}.bot3Fade-enter-active,.bot3Fade-leave-active{transition:opacity .25s ease}.bot3Fade-enter-from,.bot3Fade-leave-to{opacity:0}.bot3-slide-enter,.bot3-slide-leave-to{opacity:0;transform:translateY(-20%)}.bot3-slide-enter-to{opacity:1;transform:translateY(0)}.bot3-slide-enter-active,.bot3-slide-leave-active{transition:all .2s ease-in}</style><style type="text/css">.ibot-faq{display:flex;flex-direction:column;margin:0 auto;max-height:100%;max-width:1500px!important;overflow:auto;width:100%}.ibot-faq_embed .ibot-faq__ibot{display:none}.ibot-faq_grow{max-height:none!important}.ibot-faq__content-wrapper{position:relative}.ibot-faq__content{display:flex;flex:1;overflow:hidden}.ibot-faq__content-col2{flex:1}.ibot-faq__content-col1{width:30%}.ibot-faq__content_smallSpace .ibot-faq__content-col1{width:100%}.overflow-auto{min-height:100%;overflow-y:auto}.faq__loadingWrap{min-height:2px}.faq__loading{background:#ddd;height:2px;overflow:hidden;position:relative;width:100%}.faq__loading:before{animation:loading 2s linear infinite;background:#2980b9;content:"";height:2px;left:-200px;position:absolute;width:200px}.faq-search-block{display:flex}.faq-search-block__menu{height:50px}.faq-search-block__search{flex:1}@keyframes loading{0%{left:-200px;width:30%}50%{width:30%}70%{width:70%}80%{width:50%}95%{width:120%}to{left:100%}}</style><style type="text/css">.onebar-faq{flex-direction:column;z-index:9999}.onebar-faq-header{align-items:center;display:flex;flex-grow:1}.onebar-faq-header-title{display:inline-block}.onebar-faq-footer{align-items:center;display:flex;flex-grow:1}.onebar-faq-browse-faq-button{border-radius:99px;width:100%}.onebar-faq-body{display:flex;flex-grow:99;height:90%}</style><style type="text/css">.ibam-modal .title-wrap{margin:10px 0}.ibam-modal h5{display:inline;margin:0}.ibam-modal svg{height:25px;width:25px}</style><style type="text/css">.mta-slider-number{width:11ch}.mta-mobile-slider{flex-basis:100%!important;width:100%}</style><style type="text/css">.notifications-modal .price-alerts-content{max-height:280px}.notifications-modal .show-tz ul{display:none}.notifications-modal .pick-up-focus{max-height:0;max-width:0;overflow:hidden}</style><style type="text/css">.fa-welcome{width:100%}.fa-welcome__header{align-items:center}.fa-welcome__icon svg{height:200px;width:200px}</style><style type="text/css">.ibeos-routing-info{border-radius:.75em}</style><style type="text/css">.order-price-info .progress-color{color:#e5f3fc}.order-price-info .realtime-data-container{align-content:flex-start;display:flex;flex-wrap:wrap;height:4.09091em;line-height:1.363636}.order-price-info .bid-ask-container{align-items:flex-end;display:flex;flex-direction:column;overflow-x:auto}.order-price-info .bid-ask-container p{margin-right:auto}.order-price-info .details svg{stroke:#fff;height:1.5em;vertical-align:-.4em;width:1.5em}.order-price-info .sell-fade{-webkit-animation-duration:30s;animation-duration:30s;animation-timing-function:linear;color:#dd4d42}.order-price-info .sell-fade-light{-webkit-animation-name:sell-fade-light-loader;animation-name:sell-fade-light-loader}.order-price-info .sell-fade-light:after{color:#222}.order-price-info .sell-fade-dark{-webkit-animation-name:sell-fade-dark-loader;animation-name:sell-fade-dark-loader}.order-price-info .sell-fade-dark:after{color:#fff}@-webkit-keyframes sell-fade-light-loader{0%{color:#dd4d42}to{color:#222}}@keyframes sell-fade-light-loader{0%{color:#dd4d42}to{color:#222}}@-webkit-keyframes sell-fade-dark-loader{0%{color:#dd4d42}to{color:#fff}}@keyframes sell-fade-dark-loader{0%{color:#dd4d42}to{color:#fff}}.order-price-info .buy-fade{-webkit-animation-duration:30s;animation-duration:30s;animation-timing-function:linear;color:#52ade6}.order-price-info .buy-fade-light{-webkit-animation-name:buy-fade-light-loader;animation-name:buy-fade-light-loader}.order-price-info .buy-fade-light:after{color:#222}.order-price-info .buy-fade-dark{-webkit-animation-name:buy-fade-dark-loader;animation-name:buy-fade-dark-loader}.order-price-info .buy-fade-dark:after{color:#fff}@-webkit-keyframes buy-fade-light-loader{0%{color:#52ade6}to{color:#222}}@keyframes buy-fade-light-loader{0%{color:#52ade6}to{color:#222}}@-webkit-keyframes buy-fade-dark-loader{0%{color:#52ade6}to{color:#fff}}@keyframes buy-fade-dark-loader{0%{color:#52ade6}to{color:#fff}}[dir=rtl] .order-price-info .bid-ask-container p{margin-left:auto;margin-right:0}</style><style type="text/css">.exit-strategy-form{flex-direction:column;min-height:100%}.exit-strategy-form__left-column{flex-basis:60%}.exit-strategy-form__right-column{flex-basis:40%}.exit-strategy-form__price-button-column{flex-basis:30%}.exit-strategy-form__button-row{margin-top:auto}.exit-strategy-form__increment-button{border-top-left-radius:25%;border-top-right-radius:25%}.exit-strategy-form__decrement-button{border-bottom-left-radius:25%;border-bottom-right-radius:25%}#exit-strategy-chart svg{height:100%;width:100%}</style><style type="text/css">.circle-prg-anm .animated-confirmation{position:relative}.circle-prg-anm__arrow,.circle-prg-anm__circle-filled,.circle-prg-anm__circle-loader{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}.circle-prg-anm__circle-progress svg{animation:odr-sbm-loader-spin 3s ease .5s infinite;height:3.5em;width:3.5em}.circle-prg-anm .animated-confirmation.Filled .circle-prg-anm__circle-filled svg,.circle-prg-anm .animated-confirmation.Filled .circle-prg-anm__circle-loader svg,.circle-prg-anm .animated-confirmation.Filled .circle-prg-anm__circle-progress svg,.circle-prg-anm .animated-confirmation.Submitted .circle-prg-anm__circle-filled svg,.circle-prg-anm .animated-confirmation.Submitted .circle-prg-anm__circle-loader svg,.circle-prg-anm .animated-confirmation.Submitted .circle-prg-anm__circle-progress svg{visibility:hidden}.circle-prg-anm__arrow svg{height:2.5em;width:2.5em}.circle-prg-anm__circle-filled svg{height:3.5em;width:3.5em}.circle-prg-anm__circle-filled svg .circle{stroke:#4d6e9d;fill-opacity:0;stroke-dasharray:1000;stroke-dashoffset:1000;animation:odr-sbm-stroke 2s linear forwards}.circle-prg-anm__circle-loader{animation:odr-sbm-fadeCircle 1s ease;border-radius:50%;height:3.5em;width:3.5em;will-change:opacity}.circle-prg-anm__checkmark{animation:odr-sbm-checkmark 2s linear;border-right:4px solid #fff;border-top:4px solid #fff;height:25px;left:calc(50% - 14px);position:absolute;top:54%;transform:scaleX(-1) rotate(135deg);transform-origin:left top;width:12.5px;will-change:border-width,width,height}@keyframes odr-sbm-loader-spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}@keyframes odr-sbm-stroke{to{stroke-dashoffset:0}}@keyframes odr-sbm-fadeCircle{0%{opacity:0}70%{opacity:0}to{opacity:1}}@keyframes odr-sbm-checkmark{0%{border-width:0;height:0;width:0}80%{border-width:0;height:0;width:0}90%{border-width:3px;height:0;width:12.5px}to{border-width:3px;height:25px;width:12.5px}}.circle-prg-anm .hsbc-icons{text-align:center}.circle-prg-anm .hsbc-icons svg{height:3.5em;width:3.5em}</style><style type="text/css">.exit-strategy-preview{height:100%}</style><style type="text/css">.option-exercise-order-ticket{height:100%}.option-exercise-order-ticket__post-actions{display:flex;justify-content:space-between}.option-exercise-order-ticket__deadline{align-items:center;display:flex}</style><style type="text/css">.help-menu-labels{line-height:15px;min-height:30px;white-space:normal}.help-menu-btns{padding:10px}</style><style type="text/css">.legacy-help-popover__entry:hover{background-color:rgba(11,86,191,.05)}</style><style type="text/css">.one-head-options{margin:0;padding:8px 16px}.one-head-options>li{margin:8px;padding:0}</style><style type="text/css">.widescreen-guide img{max-height:100%;max-width:100%}.widescreen-guide ul{list-style-type:disc!important}.widescreen-guide__close{cursor:pointer;margin-right:-10px;margin-top:-10px}.widescreen-guide__header{display:flex;justify-content:space-between}</style><style type="text/css">.one-user-btn{align-items:center;height:46px;justify-content:space-between;margin:0;width:100%}.one-user-btn-accessibility-tooltip{cursor:pointer}.one-user-btn-lg{height:105px;margin:0;padding:4px;width:100%}.one-user-icon svg{height:32px;width:32px}.one-user__widescreen-list{list-style-type:disc!important}.unified-settings-icon svg{height:20px;width:20px}</style><style type="text/css">.sl-search-bar input._fldin:focus,.sl-search-bar input._fldin:focus-visible{border-color:inherit!important;box-shadow:none!important}.sl-search-bar .screen-reader-text{clip:rect(0,0,0,0);border:0;clip-path:polygon(0,0,0,0,0,0,0,0);display:block;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;visibility:visible;white-space:nowrap;width:1px}.sl-search-bar .sl-symbol-text{text-align:center}.sl-search-bar .sl-symbol-text svg{vertical-align:0}.sl-search-bar .sl-symbol-input{display:flex;justify-content:right;position:relative}.sl-search-bar .sl-symbol-input input[type=text]::-ms-clear{display:none}.sl-search-bar .sl-symbol-input .sl-input-field{border-radius:3px;box-shadow:0 .1em .1em rgba(32,33,36,.28)!important;font-family:inherit;font-size:1em;height:2.75em;padding-left:2em;padding-right:2em;width:100%}.sl-search-bar .sl-symbol-input .sl-input-field.placeholder{box-shadow:none!important}.sl-search-bar .sl-symbol-input .sl-cancel-icon,.sl-search-bar .sl-symbol-input .sl-search-conid,.sl-search-bar .sl-symbol-input .sl-search-icon{margin:0;padding:0;position:absolute;top:.8em}.sl-search-bar .sl-symbol-input .sl-cancel-icon:hover,.sl-search-bar .sl-symbol-input .sl-search-conid:hover,.sl-search-bar .sl-symbol-input .sl-search-icon:hover{cursor:pointer}.sl-search-bar .sl-symbol-input .sl-search-icon{left:auto;line-height:0;right:1em}.sl-search-bar .sl-symbol-input .sl-search-icon svg{padding-top:.125em}.sl-search-bar .sl-symbol-input .sl-search-icon.start-search-box{left:.6em;right:auto}.sl-search-bar .sl-symbol-input .sl-cancel-icon{cursor:pointer;line-height:0;right:.8em}.sl-search-bar .sl-symbol-input .sl-cancel-icon svg{fill:#999;vertical-align:1.25em}.sl-search-bar .sl-symbol-input .sl-search-conid{left:.8em;top:.6em}.sl-search-bar .sl-symbol-input .sl-search-conid:hover{cursor:default}.sl-search-bar .sl-symbol-input .sl-search-conid .close-pill-btn:hover{cursor:pointer}.sl-search-bar .sl-symbol-input .sl-search-conid-pill{border-radius:1em}.sl-search-bar .sl-symbol-input .sl-search-conid-pill .symbol-name{letter-spacing:2px}.sl-search-bar .sl-exchange-dd{box-shadow:0 .1em .1em rgba(32,33,36,.28)}.sl-search-bar .sl-symbol-input-defaults .sl-input-field:focus,.sl-search-bar .sl-symbol-input-defaults .sl-input-field:hover,.sl-search-bar .sl-symbol-input-defaults .sl-input-field:valid{border:none;width:100%}.sl-search-bar .sl-symbol-input-defaults .sl-input-field:focus~.sl-search-icon svg,.sl-search-bar .sl-symbol-input-defaults .sl-input-field:hover~.sl-search-icon svg,.sl-search-bar .sl-symbol-input-defaults .sl-input-field:valid~.sl-search-icon svg{fill:#767676}.sl-search-bar .sl-symbol-input-defaults .sl-input-field:focus{outline:2px solid #52ade6}.sl-search-bar .sl-symbol-input-theme-ntws .sl-input-field{border-radius:0}.sl-search-bar .sl-symbol-input-theme-ntws input:focus{outline:none!important}.sl-search-bar .sl-symbol-input-theme-default .sl-input-field{border:none}.sl-search-bar .sl-symbol-input-theme-default input:focus,.sl-search-bar .sl-symbol-input-theme-default select:focus,.sl-search-bar .sl-symbol-input-theme-default textarea:focus{outline:none!important}.sl-search-bar .sl-symbol-input-theme-blue .sl-input-field{background-color:#0f2440;border:1px solid #4d6e9d;color:#fff}.sl-search-bar .sl-symbol-input-theme-blue .sl-search-icon svg{fill:#fff}.sl-search-bar .sl-symbol-input-theme-onebar .sl-input-field{background-color:#011025;border:1px solid #192f4d;color:#fff}.sl-search-bar .sl-symbol-input-theme-onebar .sl-search-icon svg{fill:#fff}.sl-search-bar .sl-symbol-input-theme-tradingSettings .sl-input-field{border:1px solid #0b56bf}.sl-search-bar .sl-symbol-input-theme-tradingSettings input:focus{outline:none!important}[dir=rtl] .sl-search-bar .sl-search-icon{left:1em;right:auto}[dir=rtl] .sl-search-bar .sl-search-icon.start-search-box{left:auto;right:.6em}[dir=rtl] .sl-search-bar .sl-cancel-icon{left:.8em;right:auto}[dir=rtl] .sl-search-bar .sl-search-conid{left:auto;right:.8em}</style><style type="text/css">.sl-search-results .sl-company-asset,.sl-search-results .sl-company-header{white-space:normal}.sl-search-results .sl-company-asset{cursor:pointer;padding-right:.5em}.sl-search-results .sl-company-asset a{display:block}.sl-search-results .sl-company-container:last-child .sl-company-name,.sl-search-results .sl-company:last-child{border-bottom:none}.sl-search-results .sl-option-chain{display:inline!important}</style><style type="text/css">.sl-search-results{border-radius:3px;box-shadow:0 .5em .5em rgba(32,33,36,.28);width:100%}.sl-search-results .flex-min-child{min-width:30%}.sl-search-results .min-first-child{display:inline-block;width:8em}.sl-search-results .sl-grow{max-height:11.5em;overflow-y:auto}.sl-search-results .sl-sm-icon svg{height:.625em;vertical-align:baseline;width:.625em}.sl-search-results .more-btn:hover{text-decoration:underline}.sl-search-results-ntws{background-color:#23232d}.results-found{cursor:default;float:right}</style><style type="text/css">.header ._btn.link{font-size:.8125rem}.header ._btn.link svg{height:.8125em;margin-left:8px;vertical-align:baseline;width:1em}</style><style type="text/css">.sl-asset-options .pr8{padding-right:8px}.sl-asset-options .sl-asset-options-table{padding:1em;width:100%}.sl-asset-options .option-stack-row,.sl-asset-options .sl-asset-options-table .sl-asset-options-row{width:100%}.sl-asset-options .sl-asset-option-label{line-height:1.4em;text-align:left;white-space:normal}.sl-asset-options .sl-asset-option-label.sel-instrument{text-align:center}.sl-asset-options .sl-asset-option-label.sel-instrument svg{margin-right:8px;vertical-align:-8px}.sl-asset-options .sl-asset-option-field{width:100%}.sl-asset-options .sl-asset-option-field ._field{margin:0;width:100%}.sl-asset-options .sl-asset-option-field ul{list-style:none;margin:0;padding:0}.sl-asset-options .sl-asset-option-field ul li button{border-radius:1em;width:100%}.sl-asset-options .sl-asset-option-field ul li button:focus{background-color:#4d6e9d;color:#fff}.sl-asset-options .sl-asset-option-field ul li button .label-container{overflow:hidden;text-overflow:ellipsis}.sl-asset-options .sl-asset-header{line-height:1.2em;margin-inline-start:1em;padding:1em 0;position:relative;white-space:normal}.sl-asset-options .sl-asset-header ._btn.link{font-size:.8125rem;padding-bottom:0;padding-left:0;padding-top:.5em}.sl-asset-options .sl-asset-header ._btn.link svg{height:.8125em;margin-left:8px;vertical-align:baseline;width:1em}.sl-asset-options .sl-select-button-container{padding-top:2em}.sl-asset-options .sl-select-button{background:#fff;border-color:#5474a1;color:#5474a1;width:100%}.sl-asset-options .sl-callput{margin:0!important;width:50%}.sl-asset-options ._btn.sl-callput{border-radius:unset;text-align:unset}.sl-asset-options ._btn.sl-put{border-bottom-right-radius:.2em;border-top-right-radius:.2em;padding-right:.5em!important;text-align:right!important}.sl-asset-options ._btn.sl-call{border-bottom-left-radius:.2em;border-top-left-radius:.2em;padding-left:.5em!important;text-align:left!important}.sl-asset-options .error-text{border:1px solid red!important}.sl-asset-options__info{display:flex;justify-content:space-between}.sl-asset-options__trading-date{flex-direction:column}.sl-asset-options__default-fields{display:flex;flex:1}</style><style type="text/css">.symbol-lookup-component{min-height:2.5em;position:relative;z-index:10}.symbol-lookup-component .sl-dropdown{box-shadow:0 16px 22px rgba(0,0,0,.04),0 2px 6px 1px rgba(0,0,0,.05),0 9px 22px 6px rgba(0,0,0,.05);max-height:calc(100vh - 10em);overflow-y:auto;position:absolute;width:100%;z-index:8192}.symbol-lookup-component .trade-perm-link{display:block}.symbol-lookup-component .trade-perm-link .underline{text-decoration:underline}</style><style type="text/css">.feedbackApp__popover{align-items:stretch;display:flex}</style><style type="text/css">.userVoiceSuggestion__field{overflow:hidden;padding:0!important;padding-inline-start:16px!important}.userVoiceSuggestion__field--endBtn{border:transparent;border-radius:0!important;height:100%}@media (max-width:380px){.userVoiceSuggestion__field label{font-size:14px;white-space:normal!important}}.userVoiceSuggestion__close svg{transform:rotate(45deg)}</style><style type="text/css">.parent-stylesheet-override{color:inherit!important;font-weight:inherit!important}.userVoiceItem__reverse{flex-direction:row-reverse}.userVoiceItem__btnGroup :first-child{border-bottom-right-radius:0;border-top-right-radius:0}.userVoiceItem__btnGroup :last-child{border-bottom-left-radius:0;border-top-left-radius:0}.userVoiceItem__item strong{font-weight:inherit!important}.userVoiceItem__item--light strong{background:#f0f4fa}.userVoiceItem__item--light-web strong{background:silver!important}.userVoiceItem__item--dark strong{background:#444}.userVoiceItem__vote--cell{align-items:center;display:flex;flex-direction:column;justify-content:center;min-height:32px;width:72px}.userVoiceItem__vote--btn{border-radius:0;min-height:32px;width:72px}.userVoiceItem__clampBtn,.userVoiceItem__clamp P{display:inline}.text-semibold p{font-weight:inherit}.fg70 p{color:inherit}</style><style type="text/css">.parent-stylesheet-override{color:inherit!important;font-weight:inherit!important}.uv_disclaimer{text-align:start}.uv_disclaimer__footer{text-align:end}</style><style type="text/css">.parent-stylesheet-override{color:inherit!important;font-weight:inherit!important}.userVoiceList ._ovfm{padding:0}</style><style type="text/css">.userVoiceNew__textarea ._fldi{white-space:normal}.userVoiceNew__col{flex-direction:column}</style><style type="text/css">.tws-skeleton ._btn,.tws-skeleton a,.tws-skeleton h1,.tws-skeleton h2,.tws-skeleton h3,.tws-skeleton h4,.tws-skeleton h5,.tws-skeleton img,.tws-skeleton p,.tws-skeleton span,.tws-skeleton svg{animation-duration:2.25s;animation-fill-mode:forwards;animation-iteration-count:infinite;animation-name:intAnimSkel;animation-timing-function:linear;background:linear-gradient(to right,var(--bg-canvas) 8%,var(--bg) 18%,var(--bg-canvas) 33%);background-size:800px 104px;border-radius:5px;color:transparent!important;position:relative}.tws-skeleton circle,.tws-skeleton path{opacity:0}.tws-skeleton .userVoiceItem__vote{border:none}.tws-skeleton .userVoiceItem__vote button{box-shadow:none}.tws-skeleton ._taba{border-bottom:none}@keyframes intAnimSkel{0%{background-position:-468px 0}to{background-position:468px 0}}.userVoice__form--btmPad{padding-bottom:16px}.userVoice ._btn{box-shadow:none!important}.routeFade-enter-active,.routeFade-leave-active{transition:opacity .35s ease-in-out}.routeFade-enter-from,.routeFade-leave-to{opacity:0}</style><style type="text/css">.parent-stylesheet-override{color:inherit!important;font-weight:inherit!important}.feedbackAppGeneralFB__btnContainer{display:grid;gap:8px;grid-template-columns:repeat(auto-fit,minmax(125px,2fr))}button.feedbackAppGeneralFB__btn{align-items:center;display:flex;flex-direction:column;min-height:125px;white-space:normal}button.feedbackAppGeneralFB__ngButton{flex-direction:row;min-height:auto!important;text-align:start;white-space:normal;width:100%}</style><style type="text/css">.feedbackAppEffort__selection{opacity:0}.feedbackAppEffort__selection--show{opacity:1}.feedbackAppEffort__grid{gap:8px}</style><style type="text/css">.feedbackAppRr{align-items:center;display:flex;flex-direction:column;justify-content:space-between}.feedbackAppRr button{width:100%}.feedbackAppRr i svg{height:auto!important;max-width:100%;width:auto!important}.feedbackAppRr__ui{transition:opacity .25s ease-in-out}.feedbackAppRr__ui--loading{opacity:0}</style><style type="text/css">.feedbackState{display:flex;flex-direction:column;height:100%}.feedbackState__ico{display:flex;justify-content:center}</style><style type="text/css">.feedbackAppFull__btm--wide button{padding:12px 24px;width:100%}</style><style type="text/css">.w100{width:100%}.h100{height:100%}.w25{width:25%}.feedback-fade-enter,.feedback-fade-fast,.feedback-fade-fast-leave-to,.feedback-fade-leave-to{opacity:0}.feedback-fade-enter-active,.feedback-fade-leave-active{transition:opacity .5s}.feedback-fade-fast-enter-active,.feedback-fade-fast-leave-active{transition:opacity .15s}.feedbackApp__impact ._field._fo{border:transparent}.feedbackApp__ta{height:150px;margin:0!important;resize:none;width:100%}.feedbackApp__ico svg{height:auto;width:auto}</style><style type="text/css">.one-head{background-color:#1c212b}.one-head-inner,.one-head-mkt{align-items:center;display:flex;flex-direction:row}.one-head-mkt{flex:1 1 auto;overflow:hidden}.one-head-items{display:flex;flex:0 1 auto;flex-direction:row;overflow:hidden;padding:6px 32px}.one-head-item{position:relative}.one-head-item:hover>.one-head-edit{visibility:visible}.one-head-skip-link{opacity:0}.one-head-skip-link:focus,.one-head-skip-link:hover{opacity:1}.one-head-edit{left:-12px;position:absolute;top:-6px;visibility:hidden}.one-head-menu{display:flex;flex:0 0 auto;flex-direction:row;gap:1.2rem;padding:6px 32px}.one-head-menu.flex-end{display:flex;justify-content:flex-end}.one-head-32{-webkit-margin-end:32px;margin-inline-end:32px}.one-head-16{-webkit-margin-end:16px;margin-inline-end:16px}.one-head-bar-menu button,.one-head-bar-menu button:hover,.one-head-bar-menu span{color:#fff!important}@media only screen and (min-width:1560px){.one-head-bar-menu-absolute{position:absolute}[dir=rtl] .one-head-bar-menu-absolute{right:0}[dir=ltr] .one-head-bar-menu-absolute{left:0}}</style><style type="text/css">.estimate-price{padding-bottom:0!important;padding-top:0!important}.estimate-price .tilde{font-family:sans-serif}</style><style type="text/css">.trailing_order_unit{float:left;height:2.5em;position:relative;width:50%}.trailing_order_unit input,.trailing_order_unit label{bottom:0;display:block;left:0;position:absolute;right:0;top:0}.trailing_order_unit input[type=radio]{opacity:.011}.trailing_order_unit label{cursor:pointer;line-height:1.8em}</style><style type="text/css">.order-info .order-info__row-wrap{align-items:end;flex-wrap:wrap}</style><style type="text/css">.order-preview{padding:.5em 1em}.order-preview .block:first-child{font-size:1.2em;padding-bottom:.5em}.order-details{border-spacing:0;width:100%}.order-details tr.margin td{padding:3px 0;width:28%}.order-details td{border-collapse:collapse;border-top:1px solid silver;padding:10px 5px}.order-details.margin-table{display:block;overflow:auto}.order-details.margin-table td:first-child{width:35%}.bid-ask-container{align-items:flex-end;flex-direction:column;overflow-x:auto}p.bid-ask-container__line{margin-right:auto}.tif-label{vertical-align:baseline}.amt-conv-info{overflow-x:auto}</style><style type="text/css">.direct-routing__decoration{border-radius:.75em}</style><style type="text/css">.cp-action-menu-trigger svg{transform:rotate(90deg)}.cp-action-menu-trigger-hide{opacity:0}.cp-action-menu-trigger-show,.cp-action-menu-trigger:hover{opacity:1!important}</style><style type="text/css">.odr-sbmt{flex-direction:column;height:100%}.odr-sbmt .flex-basis50{flex-basis:50%}.odr-sbmt__button-row{margin-top:auto}.hsbc-order-submit{color:#4e6e9d}</style><style type="text/css">.dyn-acct-search{position:relative;z-index:10}.dyn-acct-search__bar{border:1px solid silver!important;box-shadow:0 .1em .1em rgba(32,33,36,.28)!important;margin:2px 3px}.dyn-acct-search__close{align-items:center;display:flex}.dyn-acct-search__close svg{fill:#999}.dyn-acct-search__input{border:none!important}.dyn-acct-search__search-icon{align-items:center;display:flex}.dyn-acct-search__search-icon svg{padding-top:.125em}.dyn-acct-search__input-container{display:flex;flex-direction:row}.dyn-acct-search__selected-search-icon svg{padding-top:.125em}.dyn-acct-search__acct-icon svg{width:1.5em}.dyn-acct-search__list{box-shadow:0 16px 22px rgba(0,0,0,.04),0 2px 6px 1px rgba(0,0,0,.05),0 9px 22px 6px rgba(0,0,0,.05);max-height:10em;overflow-y:auto;position:absolute;width:100%;z-index:8190}</style><style type="text/css">.account-selector-popover__list{max-height:20em;overflow:auto}</style><style type="text/css">.empty-account-selection-error{border-radius:5%}.faq-btn{text-decoration:underline}.default-account-dialog__container{flex-direction:column}</style><style type="text/css">table.bond-details{border-spacing:3px!important;margin-top:1em}table.bond-details th{margin-bottom:1em;text-align:left}</style><style type="text/css">.toggle_cash_shares{float:left;height:2.5em;position:relative;width:50%}.toggle_cash_shares input,.toggle_cash_shares label{bottom:0;display:block;left:0;position:absolute;right:0;top:0}.toggle_cash_shares input[type=radio]{opacity:.011}.toggle_cash_shares label{cursor:pointer;line-height:1.8em}</style><style type="text/css">.comparable-bonds{width:100%}.comparable-bonds ul{padding-left:0}.comparable-bonds ul li{border-bottom:1px solid #ccc;list-style:none}</style><style type="text/css">.fractions-modal p{margin:.5em 0!important}.fractions-modal .header{align-items:center;display:flex}.fractions-modal .close{cursor:pointer}.fractions-modal .icon svg{height:5em!important;width:5em!important}</style><style type="text/css">.fractions-label{display:table}.fractions-label>div{display:table-cell}.fractions-label .close{cursor:pointer}</style><style type="text/css">.mid-price-img{float:right;min-width:300px;width:50%}</style><style type="text/css">.qt-youraccount table{border-collapse:collapse;width:100%}.qt-youraccount td{padding:2px 0;vertical-align:baseline}</style><style type="text/css">.crypto-intro .crypto-intro-text{line-height:2em}.crypto-intro .crypto-graphic svg{height:auto;width:180px}.crypto-intro .crypto-graphic.small svg{height:auto;width:150px}</style><style type="text/css">.no-funds-icon svg,.no-permission-icon svg{height:110px;width:120px}.no-funds-icon.large-icon svg,.no-permission-icon.large-icon svg{height:119px;width:127px}</style><style type="text/css">.disconnected-crypto{border-radius:5%}</style><style type="text/css">.exit-strategy{display:flex;flex-direction:column;height:100%}.exit-strategy__left-column{flex-basis:60%}.exit-strategy__right-column{flex-basis:50%}.exit-strategy__price-field-column{flex-basis:70%}.exit-strategy__price-button-column{flex-basis:30%}.exit-strategy__button-row{margin-top:auto}.exit-strategy__line-chart svg{height:100%;width:100%}.exit-strategy__price-text-field{min-width:100%;text-align-last:end;width:70%!important}.exit-strategy__increment-button{border-top-left-radius:25%;border-top-right-radius:25%}.exit-strategy__decrement-button{border-bottom-left-radius:25%;border-bottom-right-radius:25%}</style><style type="text/css">.exit-preview{flex-direction:column;height:100%}.exit-preview__back-icon{align-self:center}.exit-preview__attach-orders-text{align-self:center;flex-basis:80%}.exit-preview .flex-basis50{flex-basis:50%}.exit-preview__button-row{margin-top:auto}.exit-preview .preview-row{display:inline-flex}.exit-preview .profit-loss-icons{align-self:center;flex-basis:20%}.exit-preview .profit-loss-icons svg{height:75%;width:75%}</style><style type="text/css">.eu-cost-modal ._mdef{overflow-y:hidden!important}</style><style type="text/css">.order-pane{-ms-overflow-style:-ms-autohiding-scrollbar!important;overflow-y:auto!important}.order-pane.h100{height:100%}.order-pane .gfis svg{vertical-align:-.45em;width:2em}.order-pane h5 a{margin-left:3px;margin-right:3px}.order-pane h5 a.selected{color:#000}.order-pane .order-info .sl-symbol-input-theme-default .sl-input-field{border:1px solid silver!important}.order-pane .order-info .symbol-lookup-component{width:100%!important}.order-pane .order-info .symbol-lookup-component .sl-dropdown{box-shadow:none;max-height:none;position:inherit}.order-pane .order-info .symbol-lookup-component .sl-dropdown .sl-asset-options .sl-asset-options-table{padding:0}.order-pane .order-info .symbol-lookup-component .sl-dropdown .sl-asset-options .sl-asset-options-table .sl-asset-option-label{font-size:.875em}.order-pane .order-info .symbol-lookup-component .sl-dropdown .sl-asset-header{border:none}.order-pane .order-info .w100{width:100%}.order-pane .order-info .mr0{margin:0}.order-pane .order-info__block{display:inline-block;padding:0 8px;width:50%}.order-pane .order-info .inline-flex{align-items:center}.order-pane .order-info .rth-box{padding-left:6em}.order-pane .order-info__moc{font-family:Proxima Nova,Verdana,Arial,sans-serif;height:2.5em}.order-pane .order-info .order-detail{margin:1em;width:18em}.order-pane .order-info .order-detail.dropdown-menu{margin-top:0;padding-left:0;position:absolute;width:unset;z-index:15}.order-pane .order-info .order-detail.dropdown-menu .executed-order{border-left:.06em solid #d9d9d9;border-right:.06em solid #d9d9d9;cursor:pointer;display:block;height:2em;line-height:2em;margin-bottom:0;padding:.2em}.order-pane .order-info .order-detail.dropdown-menu .executed-order:first-child{border:.06em solid #d9d9d9;border-bottom:none}.order-pane .order-info .order-detail.dropdown-menu .executed-order:last-child{border:.06em solid #d9d9d9;border-top:none}.order-pane .order-info .order-detail.dropdown-menu li.active{background:#4f6f9e!important;color:#fff}.order-pane .order-info ul{width:100%}.order-pane .order-info ul li .pending{border:1px solid silver;padding:3px}.order-pane .order-info ul li span.icon{padding:10px}.order-pane .order-info ul li span.icon svg{height:13px;width:13px}.order-pane .order-info ul table td{border-right:1px solid silver;padding-left:6px;padding-right:6px}.order-pane .order-info__toggle{height:2.5em}.order-pane .order-info ._dd:after{bottom:1em}.order-pane .order-info__impact-label{align-items:center;display:flex}.order-pane .submit{border-top:1px solid silver;padding:6em 22px 0}.order-pane .submit .icon{margin:1em auto}.order-pane .submit h5,.order-pane .submit p{text-align:center;width:100%}.order-pane .submit svg{height:100px;width:100px}.order-pane .error:invalid{border:1px solid red!important;color:red}.order-pane .error-text{border:1px solid red!important;color:red}.warning-message{display:inline-flex;padding:1em!important}.warning-message svg{height:30px;width:30px}.warning-message .warning-text{padding-left:.5em}.button-container{background-color:#eee}.request-status{display:table;margin:auto;padding:.5em 0}.qt-field{height:2.5em;margin:0;max-width:193px}.gifs-disclaimer{align-items:center}@media screen and (max-width:768px){._pane.order-pane .order-info .mobile-row{float:left;width:100%}._pane.order-pane .order-info span.mobile-label{display:block;padding-top:.2em;text-align:left}}</style><style type="text/css">.cash-qty-disclaimer-text{width:85%}</style><style type="text/css">.history-chart-controls .button-controls svg{height:1em;width:1em}.history-chart-controls .button-controls ._toggle{margin-bottom:0;margin-top:0}</style><style type="text/css">.ccy__baseCurrency-loading{margin-top:20px}.ccy__baseCurrency-line{margin-top:20px;position:relative}.ccy__baseCurrency-line:after{background:inherit;border-bottom:1px solid;border-right:1px solid;border-color:inherit;content:"";height:20px;left:40px;position:absolute;top:-9px;-moz-transform:rotate(45deg);-webkit-transform:rotate(45deg);transform:rotate(45deg);width:20px}.ccy__baseCurrency-line .baseCurrency-value{background:inherit;position:absolute;right:.8em;top:-9px}.ccy__subtext{width:60%}.ccy__subtext.hidden{visibility:hidden}.ccy__loading{align-items:center;display:flex;justify-content:center;width:100%}.ccy__resetBtn{justify-content:flex-end}.ccy__preview-table{overflow-x:hidden}.ccy__side-pane-chart .highcharts-container{max-width:390px}[dir=rtl] .ccy__baseCurrency-line:after{left:auto;right:40px}[dir=rtl] .ccy__baseCurrency-line .baseCurrency-value{left:.8em;right:auto}</style><style type="text/css">.close-currency-disclaimer .icon{margin:1em auto}.close-currency-disclaimer__currency-exchange-icon svg{height:101px;width:99px}</style><style type="text/css">.convert-currency-pane .warning-banner{margin:0 -.5em}.convert-currency-pane.h100{height:100%}.convert-currency-pane.light-scroll{-ms-overflow-style:-ms-autohiding-scrollbar!important;overflow-y:auto!important;padding:0 .5em}.convert-currency-pane .close{padding:0}.convert-currency-pane .submit .icon{margin:1em auto}.convert-currency-pane .submit h5{text-align:center;width:100%}.convert-currency-pane .submit svg{height:100px;width:100px}.convert-currency-pane .ccy-conv-submit-btn{width:50%}</style><style type="text/css">.prefund-modal__header{flex:1 1 auto}.prefund-modal__frame{border:none;width:100%}.prefund-modal_dlgh{border:none}</style><style type="text/css">ul.prefund-info-list{list-style-type:disc}</style><style type="text/css">.px-ticket-row{align-items:baseline;display:flex}.px-ticket-selsym{display:inline-block;width:60px}.px-ticket-badge svg{height:1.2em;width:auto}.px-ticket-asset{font-size:87.5%}.px-ticket-gauge{border-radius:4px;display:flex;height:4px;overflow:hidden}.px-ticket-gauge>:first-child{margin-inline-start:-4px}.px-ticket-gauge>:last-child{margin-inline-end:-4px}.px-ticket-gask,.px-ticket-gbid{min-width:10px;transform:skew(-30deg);transition:flex .5s}.px-ticket-gbid{margin-inline-end:1px}.px-ticket-gask{margin-inline-start:1px}.px-ticket-line{align-items:baseline}</style><style type="text/css">.px-view{align-items:center;justify-content:center}.px-view-table{align-self:stretch}</style><style type="text/css">.px-ticket-row{align-items:baseline;display:flex}.px-ticket-selsym{display:inline-block;width:60px}.px-ticket-badge svg{height:1.2em;width:auto}.px-ticket-asset{font-size:87.5%}.px-ticket-gauge{border-radius:4px;display:flex;height:4px;overflow:hidden}.px-ticket-gauge>:first-child{margin-inline-start:-4px}.px-ticket-gauge>:last-child{margin-inline-end:-4px}.px-ticket-gask,.px-ticket-gbid{min-width:10px;transform:skew(-30deg);transition:flex .5s}.px-ticket-gbid{margin-inline-end:1px}.px-ticket-gask{margin-inline-start:1px}.px-ticket-line{align-items:baseline}</style><style type="text/css">.account-select{border:none;border-radius:1em}.account-number{border-radius:0}.account-number,.account-number:hover{background-color:#ededed!important}</style><style type="text/css">.close-position__ml-0{margin-left:0}.close-position__icon{margin-top:8em}.close-position__icon svg{height:100px;width:100px}.close-position__inline-blk{max-width:50%!important}.close-position__mw-100{max-width:100%!important}.close-position__min-50{min-width:50%}[dir=rtl] .close-position__offset-input input{direction:ltr;text-align:end}</style><style type="text/css">.partial-interruption-bar{background-color:#fcf8e2}</style><style type="text/css">.infoViewer{background:transparent;cursor:pointer;overflow:hidden;width:100%}.infoViewer img{width:100%}.infoViewer__placeholder{align-items:center;background:rgba(77,110,157,.3);display:flex;flex-direction:column;font-family:sans-serif;height:100%;justify-content:center;width:100%}.infoViewer__placeholder--message{font-size:14px;margin-top:0}.infoViewer__placeholder h2{margin-top:0}.infoViewer__placeholder--rev{font-family:sans-serif;font-size:.2em;margin:3px 0 0;opacity:.3}</style><style type="text/css">.navBar-pane{background-color:#202934!important}.navBar-pane .navBar-content{height:100%;overflow-y:auto}.navBar-pane-nav-subitem{display:flex;position:relative}.navBar-pane-nav-subitem__active{border-radius:6px;height:6px;position:absolute;top:8px;width:6px}.navBar-pane-nav-subitem__newTag{height:20px!important;margin:auto!important}[dir=rtl] .navBar-pane-nav-subitem__active{right:-11px}[dir=ltr] .navBar-pane-nav-subitem__active{left:-11px}.navBar-pane .nav-item:hover{background-color:#151a20!important;color:#fff}.navBar-pane ._tag:before{z-index:0}.navBar-pane ._tag ._tagi{z-index:1}.navBar-pane .navBar-infoViewer{margin:0 auto;width:100%}.navBar-pane .navBar-close button:focus,.navBar-pane .navBar-close button:hover{color:#fff!important}.navBar-pane .navBar__active{background-color:#0b0e11!important}.navBar-pane .navBar__contact-header{background-color:#151a20!important}.navBar-pane .contact-pill{color:#fff!important}.navBar-pane .navBar-level3{-webkit-padding-start:48px;padding-inline-start:48px}.navBar-pane .navBar__expand{color:#8d9cbb!important}</style><style type="text/css">.bulletin-modal .nrp{padding-right:0}.bulletin-modal .pb1{padding-bottom:1em}.bulletin-modal .arrows a.disabled{cursor:default}.bulletin-modal .arrows a.disabled svg *{stroke:#c5c5c5}.bulletin-modal .arrows a{margin:0 5px;vertical-align:top}.bulletin-modal .arrows svg{height:1.5em;width:1.5em}.bulletin-modal .arrows svg *{stroke-width:2;stroke:#000}.bulletin-modal .bulletin-container{height:100%}.bulletin-modal .bulletin-icon svg{fill:#ffda0d;height:1.5em;width:1.5em}.bulletin-modal__content{overflow-x:auto}</style><style type="text/css">.notif-modal .arrows svg{height:1.5em;width:1.5em}.notif-modal .arrows svg *{stroke-width:2;stroke:#000}.notif-modal .notif-container{height:100%}</style><style type="text/css">.one-notify-list{list-style:none;margin:0;padding:0}.one-notify-list a{display:block;text-decoration:none!important}.one-notify-list li:hover{background-color:rgba(82,173,230,.05)}</style><style type="text/css">.notifications-component{flex-direction:column;height:100%}.notifications-component__secureMessageCenter{margin-top:auto}.notifications-component__desc{-webkit-line-clamp:3;-webkit-box-orient:vertical;display:-webkit-box;line-height:1.25em;max-height:3.75em;overflow:hidden}.notifications-component__bullet{border-radius:1em;height:.5em;width:.5em}.notifications-component__container{overflow-y:auto;width:100%}.notifications-component__count{position:-webkit-sticky;position:sticky;top:0;z-index:10}.notifications-component__read{opacity:0}.notifications-component__notification:hover .notifications-component__read{opacity:1!important}</style><style type="text/css">.nav-bar-popover-column{min-width:180px!important}</style><style type="text/css">.nav-items-container{column-gap:3rem;display:flex;flex-wrap:wrap;row-gap:1rem}.nav-item:focus:not(:disabled){color:var(--fg)!important}.nav-item{position:relative}.nav-item-badge{position:absolute;top:-10px}</style><style type="text/css">.welcome-video{position:relative}.welcome-play{background:linear-gradient(hsla(0,0%,88%,.5),rgba(0,0,0,.4));border:2px solid #fff;box-shadow:4px 4px 16px rgba(0,0,0,.2);height:60px;left:50%;padding:0 24px 0 12px;position:absolute;text-shadow:2px 2px 8px rgba(0,0,0,.3);top:50%;transform:translate(-50%,-50%)}.welcome-play svg{vertical-align:-10px}.welcome-footercol{padding-left:130px!important;position:relative}.welcome-sideimg{height:auto;left:0;max-width:140px;position:absolute;top:-32px}.welcome-getimg{height:28px;width:auto}#welcome-cp-modal .darkred{color:#b20e1d}#welcome-cp-modal .bg-darkred{background-color:#b20e1d}#welcome-cp-modal .welcome-content img,#welcome-cp-modal .welcome-content video{height:66.6vw;max-height:400px;max-width:100%}#welcome-cp-modal .welcome-content ul{list-style-type:inherit;padding-bottom:1em;padding-left:40px}#welcome-cp-modal .welcome-content ul.ibkr-list{list-style-type:none;padding-left:0}#welcome-cp-modal .welcome-content ul.ibkr-list li{margin:1em 0}@media only screen and (max-width:769px){#welcome-cp-modal .welcome-content img,#welcome-cp-modal .welcome-content video{max-height:100%}}</style><style type="text/css">.menu-help-modal ._dialog{border-radius:3px;left:21em;margin:0;position:fixed;width:350px!important}.menu-help-modal .header{padding-top:.5em}.menu-help-modal .container img{width:100%}.menu-help-modal .container p span svg{fill:currentColor!important;height:1em;margin:0 .5em;width:1em}.menu-help-modal ._btn.accent{display:block;line-height:2;margin:1em 1em 2em;width:100%}.menu-help-modal .left-arrow{border-bottom:10px solid transparent;border-right:10px solid #fff;border-top:10px solid transparent;height:0;left:-10px;position:absolute;top:4em;width:0}</style><style type="text/css">.ib-bar3 .nav-items__next-row{display:flex;justify-content:center}.ib-bar3 .justify-center{justify-content:center}.ib-bar3 .nav-container{align-items:center;display:flex;min-height:5rem}.ib-bar3 .search-icon{padding-bottom:2px}.ib-bar3[dir=ltr] .search-icon{padding-left:2rem}.ib-bar3[dir=rtl] .search-icon{padding-right:2rem}.ib-bar3 .bar-fg{color:#fff!important}.ib-bar3__sl-container{position:absolute!important;width:-moz-available;width:-webkit-fill-available}[dir=rtl] .ib-bar3__sl-container{left:0}[dir=ltr] .ib-bar3__sl-container{right:0}.ib-bar3__middle-container{display:flex;justify-content:end;min-width:4rem;position:relative}.ib-bar3__middle-container,.ib-bar3__w-avail{width:-moz-available;width:-webkit-fill-available}.ib-bar3__trade-btn-container{display:flex;flex:1;justify-content:end}.ib-bar3__sl-outer-container{height:100%;position:relative;width:-moz-available;width:-webkit-fill-available}.ib-bar3__sl-expand{min-width:500px;position:absolute;width:100%;z-index:10}.ib-bar3 .sl-close-button{position:absolute;right:0;top:.2em;z-index:15}.ib-bar3 .sl-close-button.lg-screen{right:.75em;top:.8em}.ib-bar3 .sl-close-button svg{fill:#999}.ib-bar3 .sl-close-button__mobile{position:relative;right:1.7em;top:-.1em}.ib-bar3 .sl-cancel-icon{display:none}.ib-bar3__mobile-sl{display:flex;justify-content:center;position:absolute;top:3em;width:100%;z-index:1017}.ib-bar3__mobile-sl>div{min-width:85%}.ib-bar3 .bar3-logo{display:flex;position:relative}.ib-bar3 .bar3-logo__ib{position:relative}.ib-bar3 .bar3-logo__ib img,.ib-bar3 .bar3-logo__ib svg{height:40px;width:auto}.ib-bar3 .bar3-logo__ib.fg.a__inherit-color:active,.ib-bar3 .bar3-logo__ib.fg.a__inherit-color:hover,.ib-bar3 .bar3-logo__ib.fg.a__inherit-color:visited{color:inherit}.ib-bar3 .bar3-logo__mobile{align-items:center;height:60px;justify-content:space-between;width:100%}.ib-bar3 .bar3-logo__mobile img,.ib-bar3 .bar3-logo__mobile svg{height:32px}.ib-bar3 .bar3-tabs ._tabtn{opacity:.7}.ib-bar3 .bar3-tabs ._tabtn:hover{opacity:1}.ib-bar3__down-arrow{font-size:.5em}.ib-bar3__mask-button{opacity:0;pointer-events:none}.ib-bar3__ver{pointer-events:none;position:absolute;right:-24px;top:0}[dir=rtl] .ib-bar3 .sl-close-button{left:0;right:unset}[dir=rtl] .ib-bar3 .sl-close-button__mobile{left:1.7em;right:unset}[dir=rtl] .ib-bar3 .sl-close-button.lg-screen{left:.75em;right:auto}</style><style type="text/css">.ib-bar-footer__menu{-webkit-tap-highlight-color:transparent}</style><style type="text/css">.cp__hide_am_submenu [data-fa-overlay-menu=true]{display:none!important}</style><style type="text/css">.paxos-badge svg{width:2.25em}.zh-badge svg{width:3.5em}.crypto-badge-align svg{vertical-align:-.25em}.crypto-provider-badge{display:inline-block}</style><style type="text/css">.pl10{padding-left:10px}.m1em{margin:1rem auto}.w5em{width:5em}.w10em{width:10em}.w20px{width:20px}.w50{width:50%}.w33{width:33%}.p5p{padding:5px}.ml20{margin-left:10px}.mw5{margin:0 5px}.mb50{margin-bottom:50px}.unimportant{color:#8c8c8c}.inline{display:inline}.float-right{float:right}.float-left{float:left}.w150p{width:150px}.w100{width:100%}.rspace5{margin-right:5px}.bt1{border-top:1px solid #ccc}.br1{border-right:1px solid #ccc}.mb1{margin-bottom:1em}.tbp1{padding:1em 0}.vtop{vertical-align:top}.vbottom{vertical-align:bottom}._col.content{padding-bottom:5px;padding-top:5px}.progress{z-index:auto!important}._pane{-webkit-transition:opacity .2s ease-out,-webkit-transform .2s ease-out!important;transition:opacity .2s ease-out,transform .2s ease-out!important}svg.icon25{height:25px;width:25px}.font-bold{font-weight:600}.font-xl{font-size:1.75em!important}.font-larger{font-size:1.375em!important}.font-large{font-size:1.25em!important}.font-base{font-size:1em!important}.font-small{font-size:.875em!important}.font-smaller{font-size:.75em!important}.font-xs{font-size:.625em!important}.gray40{color:#666}.gray60{color:#999}.bi-modal .tab-content{height:300px}.pro-only{color:#999;font-size:13px;text-transform:uppercase}.md-blur ._app>footer,.md-blur ._app>header,.md-blur ._app>main{-webkit-filter:blur(8px);filter:blur(8px);pointer-events:none}.none{display:none}.inline-block{display:inline-block}.cp-flex{display:flex}.cp-bg-transparent{background-color:transparent}.cp-text-underline{text-decoration:underline}.cp-text-capitalize{text-transform:capitalize}.cursor,.pointer{cursor:pointer}.num{-webkit-font-feature-settings:"tnum" 1;-moz-font-feature-settings:"tnum" 1;font-feature-settings:"tnum" 1}table.collapse{border-collapse:collapse}table.collapse td{padding:0}.table{display:table;width:100%}.table .h5{margin:0;padding:0}.cell{display:table-cell}.mobile{display:none!important}.desktop{display:block}select.simple{appearance:none;-moz-appearance:none;-webkit-appearance:none;background:transparent;border:none;text-align:right;text-align-last:right}select.simple::-ms-expand{display:none}.screen-reader-text{clip:rect(0,0,0,0);border:0;clip-path:polygon(0,0,0,0,0,0,0,0);display:block;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;visibility:visible;white-space:nowrap;width:1px}.cp-widget{height:21.25em!important;overflow-y:hidden}.cp-arrow-link svg{stroke:currentColor;height:1.25em;width:1.25em}@media screen and (max-width:991px){.mcell{display:block!important}.desktop{display:none!important}.mobile{display:block!important}}.show-xs-b{display:none}@media screen and (max-width:320px){.hide-xs{display:none!important}.show-xs{display:inline!important}.show-xs-b{display:block!important}}.vmiddle{vertical-align:middle}</style><style type="text/css">.inline-block{display:inline-block}.m-vpadding{padding:.4em 2em!important}.bottom{bottom:0;width:100%}.inline{display:inline}.float-right{float:right}.cursor{cursor:pointer}.pw5{padding-left:5px;padding-right:5px}.apps-icons svg *{fill:#4d6e9d}.one-font-large{font-size:2.2em!important}.one-font-medium{font-size:1.1em!important}.mobile{display:none!important}.desktop{display:block}@media screen and (max-width:991px){.desktop{display:none!important}.mobile{display:block!important}}</style><style type="text/css">.inline-block{display:inline-block}.m-vpadding{padding:.4em 2em!important}.bottom{bottom:0;width:100%}.inline{display:inline}.float-right{float:right}.cursor{cursor:pointer}.pw5{padding-left:5px;padding-right:5px}.apps-icons svg *{fill:#4d6e9d}.one-font-large{font-size:2.2em!important}.one-font-medium{font-size:1.1em!important}.mobile{display:none!important}.desktop{display:block}@media screen and (max-width:991px){.desktop{display:none!important}.mobile{display:block!important}}</style><script src="/lib/ibot/widget.js?e0c0032b"></script><style type="text/css">.fyiTile__buttons {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.fyiTile__buttons button {
  margin-left: 0!important;
}
.fyiTile__buttons button.link {
  padding-left: 0!important;
  padding-right: 0!important;
}
.fyiTile__buttonsIcon {
  margin-left: 7px;
}
</style><style type="text/css">.fyiTile__textLine {
  min-height: 10px;
}
.fyiTile__textLine:last-child {
  margin-bottom: 10px;
}
.fyiTile__textLine p {
  color: unset !important;
  margin: 5px 0 !important;
}
</style><style type="text/css">.fyiTile__header {
  text-transform: uppercase;
}
</style><style type="text/css">.fyiRegularTileWrapper {
  cursor: pointer;
  position: relative;
  margin-right: 16px;
  background: #fff;
  padding-bottom: 3px !important;
  border-width: 2px;
}
.ibot-dark .fyiRegularTileWrapper {
  background: #444;
}
</style><style type="text/css">.fyiControlTileWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  flex-direction: column;
  margin-right: 16px;
}
</style><style type="text/css">.fyiObsoleteTileWrapper {
  cursor: pointer;
  position: relative;
  background: #fff;
  margin-right: 32px;
  padding-bottom: 3px !important;
  border-width: 2px;
  margin-left: 16px;
}
.ibot-dark .fyiObsoleteTileWrapper {
  background: #444;
}
</style><style type="text/css">.fyiFeedbackFade-enter-active,
.fyiFeedbackFade-leave-active {
  transition: opacity .5s;
}
.fyiFeedbackFade-enter,
.fyiFeedbackFade-leave-to {
  opacity: 0;
}
.fyiTile__feedbackLine {
  cursor: pointer;
}
.fyiTile__feedbackLine span {
  margin-left: 5px;
}
</style><style type="text/css">.fyiIntroWrapper {
  padding-bottom: 80px!important;
  margin-left: -16px;
  margin-bottom: -70px !important;
  position: relative;
  top: -1px;
  background: #8196be;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.fyiIntroWrapper .fyiTile__text {
  float: left;
  color: #fff!important;
  margin-top: 0;
}
.fyiIntroWrapper .fyiTile__textLine {
  margin-top: 7px !important;
}
.fyiIntroWrapper .fyiTile__header {
  font-size: 1.375rem !important;
  line-height: 1.22727273 !important;
  color: #fff !important;
  text-transform: none;
}
.fyiIntroWrapper .fyiTile__buttons {
  position: absolute;
  top: 10px;
  right: 10px;
}
.ibot-dark .fyiIntroWrapper {
  background: #333;
}
</style><style type="text/css">.fyiTile__linkLikeHeader {
  padding-bottom: 10px;
  margin-bottom: 15px;
}
</style><style type="text/css">.fyiDisclaimer__content {
  position: relative;
  margin-right: 16px;
  background: #fff;
}
.fyiDisclaimer__next {
  height: 14px;
  transform: scale(0.96);
  position: relative;
  top: 1px;
  box-shadow: 0px 1px 2px 0px #ddd5d5;
}
.fyiDisclaimer .fyiTile__buttons {
  flex-direction: row;
  justify-content: space-between;
}
.ibot-dark .fyiDisclaimer__content {
  background: #444;
}
.ibot-dark .fyiDisclaimer__next {
  box-shadow: 0px 1px 2px 0px #473a3a;
}
</style><style type="text/css">.fyiTile__lineChart svg {
  width: auto;
  height: auto;
}
.ibot-dark .fyiTile__lineChart {
  filter: invert(85%);
}
</style><style type="text/css">.left_icon img,
.right_icon img {
  width: 32px !important;
  height: 32px !important;
  max-width: unset !important;
}
.exchange {
  position: relative;
  overflow: hidden;
  padding: 0 16px;
}
.exchange > div {
  padding: 1px 0;
  /** Prevent child margins from bubbling */
}
.exchange .tiles {
  width: 100%;
}
.exchange .msg-send {
  padding: 12px 1rem 12px 0;
  margin: 0 -1rem 16px 0;
  border-bottom: 1px solid rgba(128, 128, 128, 0.4);
  -webkit-animation: msg-send-in 0.5s linear;
  animation: msg-send-in 0.5s linear;
}
.exchange .msg-send p > * {
  display: -moz-inline-stack;
  display: inline-block;
}
.exchange .msg-recv {
  position: relative;
  width: 100%;
  clear: both;
  -webkit-tap-highlight-color: transparent;
  -webkit-animation: msg-recv-in 0.75s cubic-bezier(0.2, 0.2, 0.2, 1);
  animation: msg-recv-in 0.75s cubic-bezier(0.2, 0.2, 0.2, 1);
}
.exchange .avatar {
  margin: 16px 0;
}
.exchange .avatar a {
  text-overflow: ellipsis;
}
.exchange .avatar a > svg {
  width: 17px;
  height: 14px;
  margin: 0 4px -2px;
}
.exchange .msg-text.caption {
  font-size: 0.85714286rem;
}
.exchange .msg-intent {
  position: absolute;
  width: 5em;
}
.exchange .msg-intent + div {
  margin-left: 6.5em;
  border-top: 1px solid transparent;
}
.exchange .msg-intent + div > :first-child {
  margin-top: 5px;
}
.exchange .msg-intent + div > :first-child > p {
  margin-top: 8px;
}
.exchange .msg-intent + div > :last-child {
  margin-bottom: 5px;
}
.exchange .msg-intent + div > :last-child > p {
  margin-bottom: 8px;
}
.exchange .msg-contender {
  margin: 8px 0;
  min-height: 3em;
  border-bottom: 1px solid rgba(128, 128, 128, 0.4);
}
.exchange .msg-contender .avatar {
  display: none;
}
.exchange .message-table {
  /** msg-table */
  padding-bottom: 1em;
  /** msg-table column styles */
}
.exchange .message-table .msg-table {
  overflow-x: auto;
  white-space: nowrap;
}
.exchange .message-table .msg-table table {
  width: 100%;
  border-spacing: 0;
  overflow: hidden;
  display: table;
}
.exchange .message-table .msg-table th {
  text-align: center;
  opacity: .5;
  background: none;
}
.exchange .message-table .msg-table th > p {
  margin: 0;
  padding: 1px 4px;
}
.exchange .message-table .msg-table td {
  text-align: right;
  white-space: normal;
}
.exchange .message-table .msg-table td.news {
  text-align: left;
  height: auto;
}
.exchange .message-table .msg-table th,
.exchange .message-table .msg-table td {
  vertical-align: baseline;
}
.exchange .message-table .msg-table td > p {
  margin: 0;
  padding: 2px 8px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.exchange .message-table .msg-table td.hr {
  border: 0;
  height: 16px;
}
.exchange .message-table .msg-table td:nth-child(2),
.exchange .message-table .msg-table td.key,
.exchange .message-table .msg-table th.thspan {
  text-align: left;
}
.exchange .message-table .msg-table svg {
  max-width: 24px;
  height: 20px;
  vertical-align: middle;
}
.exchange .message-table .msg-table td,
.exchange .message-table .msg-table td > p {
  max-width: 72vw;
}
.exchange .message-table .msg-table td:nth-child(2),
.exchange .message-table .msg-table td:nth-child(3) {
  max-width: calc(50vw - 8px);
}
.exchange .message-table .msg-table-list tr:first-child td {
  border-top: 1px solid #808080;
}
.exchange .message-table .msg-table-list td {
  border-bottom: 1px solid #808080;
}
.exchange .message-table .msg-table-list td:first-child {
  width: 0;
  padding: 0;
  visibility: collapse;
}
.exchange .message-table .msg-table-list td:nth-child(2) > p {
  padding-left: 0;
  opacity: .6;
}
.exchange .message-table .msg-table-list td:last-child > p {
  padding-right: 0;
}
.exchange .message-table .msg-table-shaded tr:nth-child(2n) {
  background: rgba(160, 160, 160, 0.15);
}
.exchange .message-table .msg-table-shaded tr.hi {
  background: rgba(160, 160, 160, 0.25);
}
.exchange .message-table td:not(.rg):not(.chg) {
  background: none !important;
}
.exchange .message-table td.chg.bg15-negative {
  padding-left: 12px;
  background-image: url("data:image/svg+xml,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%228%22%20height%3D%228%22%20viewBox%3D%220%200%208%208%22%20fill%3D%22rgba(255%2C255%2C255%2C0.5)%22%3E%3Cpath%20d%3D%22M0%2C0H8L4%2C8%22%2F%3E%3C%2Fsvg%3E%0A");
  background-position: 5px;
  background-repeat: no-repeat;
}
.exchange .message-table td.chg.bg15-positive {
  padding-left: 12px;
  background-image: url("data:image/svg+xml,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%228%22%20height%3D%228%22%20viewBox%3D%220%200%208%208%22%20fill%3D%22rgba(255%2C255%2C255%2C0.5)%22%3E%3Cpath%20d%3D%22M0%2C8H8L4%2C0%22%2F%3E%3C%2Fsvg%3E%0A");
  background-position: 5px;
  background-repeat: no-repeat;
}
.tiles__withIcons {
  display: flex;
}
.spin-wrap > div {
  position: relative;
  float: right;
  width: 60px;
  height: 60px;
}
@-webkit-keyframes msg-send-in {
0%,
  25% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes msg-send-in {
0%,
  25% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@-webkit-keyframes msg-recv-in {
0% {
    opacity: .01;
    -webkit-transform: translateY(30vh);
    transform: translateY(30vh);
}
100% {
    opacity: 1;
}
}
@keyframes msg-recv-in {
0% {
    opacity: .01;
    transform: translateY(30vh);
}
100% {
    opacity: 1;
}
}
/** Background colors */
.alternative1 {
  background: rgba(128, 128, 128, 0.15) !important;
}
.alternative2 {
  background: rgba(128, 128, 128, 0.35) !important;
}
.alternative3 {
  background: rgba(128, 128, 128, 0.65) !important;
}
.highlight {
  background: #fff4a2;
}
/** Foreground colors */
.Pale_Purple {
  color: #ba9bb9;
}
.Purple {
  color: #a754a4;
}
.Orange {
  color: #f80;
}
.Dark-Blue {
  color: #22a;
}
.Dark_Grey {
  color: #444;
}
.Green {
  color: #0b0;
}
.Grey {
  color: #888 !important;
}
.Light-Blue {
  color: #55f;
}
.Pink {
  color: #e5e;
}
.Maroon {
  color: #b03060;
}
.Red {
  color: #f00;
}
.Dark_green {
  color: #070;
}
.Light-Green {
  color: #2e2;
}
.Cancel-Comp {
  color: #ba2;
}
.highlight {
  color: #000;
}
</style><style type="text/css">.msg-busy {
  position: absolute;
  left: 16px;
  -webkit-animation: msg-busy-in 1s linear;
  animation: msg-busy-in 1s linear;
}
.msg-busy span {
  display: inline-block;
  display: -moz-inline-box;
  vertical-align: middle;
  width: .6em;
  height: .6em;
  border-radius: .3em;
  -webkit-animation: msg-busy-cycle 1s infinite;
  animation: msg-busy-cycle 1s infinite;
  opacity: .3;
}
.msg-busy span:nth-child(1) {
  animation-delay: 0.66s;
}
.msg-busy span:nth-child(2) {
  animation-delay: 1s;
}
.msg-busy span:nth-child(3) {
  animation-delay: 1.33s;
}
@-webkit-keyframes msg-busy-in {
0% {
    opacity: 0;
    visibility: hidden;
}
40% {
    opacity: 0;
    visibility: visible;
}
100% {
    opacity: 1;
    visibility: visible;
}
}
@keyframes msg-busy-in {
0% {
    opacity: 0;
    visibility: hidden;
}
40% {
    opacity: 0;
    visibility: visible;
}
100% {
    opacity: 1;
    visibility: visible;
}
}
@-webkit-keyframes msg-busy-cycle {
0% {
    opacity: 0.3;
}
30% {
    opacity: 0.8;
}
100% {
    opacity: 0.3;
}
}
@keyframes msg-busy-cycle {
0% {
    opacity: 0.3;
}
30% {
    opacity: 0.8;
}
100% {
    opacity: 0.3;
}
}
</style><style type="text/css">.action-links .msg-link > a span {
  pointer-events: none;
  margin: 4px 0;
  max-height: 2.5em;
  overflow: hidden;
}
.action-links .msg-link > a p {
  display: inline-block;
  margin: 0;
  vertical-align: middle;
  max-width: calc(100% - 1.25em);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.action-links .msg-link > a svg {
  pointer-events: none;
  width: 0.75em;
  height: 0.75em;
  vertical-align: middle;
  margin: 0 0 2px 0.5em;
}
.action-links .msg-link > span p {
  margin: 4px 0;
  max-height: 2.5em;
  overflow: hidden;
}
</style><style type="text/css">.msg-action {
  padding-top: 1em;
  width: 100%;
  -webkit-animation: msg-action-in 2s cubic-bezier(0.2, 0.2, 0.2, 1);
  animation: msg-action-in 2s cubic-bezier(0.2, 0.2, 0.2, 1);
}
.msg-action ul {
  margin: 0 !important;
  padding: 0 !important;
  padding-inline-start: 0 !important;
}
.msg-action li {
  list-style-type: none;
}
.msg-action ._label {
  margin-left: .5em;
}
.msg-action ._label.highlight {
  margin-left: .25em;
  padding: 0 .25em;
}
.msg-action .link {
  margin-left: .25em;
}
.msg-action ._icon {
  margin: 0 .5em;
}
.msg-action .action-button {
  min-width: 28px;
}
@-webkit-keyframes msg-action-in {
0%,
  30% {
    -webkit-transform: translateY(4rem);
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes msg-action-in {
0%,
  30% {
    -webkit-transform: translateY(4rem);
    transform: translateY(4rem);
    opacity: 0;
}
100% {
    opacity: 1;
}
}
.msg-action .action > span:first-child {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: .5em;
  max-width: 100%;
}
.msg-action .action > span:first-child > * {
  vertical-align: middle;
}
.msg-action .action .shortcut {
  padding: 0 4px;
  opacity: .5;
  font-size: 0.9em;
}
</style><style type="text/css">.msg-contract {
  margin: 8px 0;
  padding-bottom: 4px;
  white-space: nowrap;
}
.msg-contract__header {
  display: flex;
  flex-wrap: wrap;
}
.msg-contract .msg-contract-name {
  margin: 0 0 2px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.msg-contract .msg-contract-ticker {
  margin-right: 0.5rem;
  font-weight: 600;
  min-width: 12px;
}
.msg-contract .msg-contract-coname {
  font-weight: 400;
  min-width: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.msg-contract .msg-contract-extPosHolder {
  margin-left: 0.5rem;
}
.msg-contract .msg-contract-last {
  line-height: 1.28571429;
  letter-spacing: -1px;
}
.msg-contract .msg-contract-change {
  margin-left: 0.5rem;
}
.msg-contract .msg-contract-change.fg-negative::before {
  content: '-';
  margin-right: .25em;
}
.msg-contract .msg-contract-change.fg-positive::before {
  content: '+';
  margin-right: .25em;
}
</style><style type="text/css">.msg-clue {
  position: relative;
}
.msg-clue .clue-bar {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6px;
}
.msg-clue .clue-action {
  text-decoration: underline;
  text-align: right;
}
.msg-clue .clue-action:hover {
  cursor: pointer;
}
</style><style type="text/css">/** msg-feedback */
.msg-feedback {
  text-align: right;
}
.msg-feedback button {
  padding: 2px 8px;
  border: 0;
  margin: 0 1px;
  color: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}
.msg-feedback button:hover {
  border: none;
}
.msg-feedback button.positive-btn {
  margin-left: 8px;
  border-radius: 5px 0 0 5px;
  opacity: .75;
}
.msg-feedback button.positive-btn:hover {
  opacity: 1;
  color: #fff;
}
.msg-feedback button.negative-btn {
  border-radius: 0 5px 5px 0;
  opacity: .75;
}
.msg-feedback button.negative-btn:hover {
  opacity: 1;
  color: #fff;
}
.msg-feedback button.field {
  font-size: 1rem;
}
.msg-feedback button.field span {
  display: block;
  width: 100%;
  height: 100%;
}
.msg-feedback .field {
  margin: 0;
  vertical-align: middle;
  font-size: 0.7875rem;
  height: 18px;
}
.msg-feedback svg {
  height: 100%;
  float: left;
}
.msg-feedback svg line,
.msg-feedback svg path {
  stroke: currentColor;
  fill: none;
}
.msg-feedback .checkmark svg path {
  stroke: currentColor;
  fill: currentColor;
}
.msg-feedback .grow-enter-active,
.msg-feedback .grow-leave-active {
  transition: all 0.35s;
}
.msg-feedback .grow-enter-active .field,
.msg-feedback .grow-leave-active .field {
  transition: all 0.35s;
}
.msg-feedback .grow-enter .field {
  transform: translateX(100%);
}
.msg-feedback .grow-leave-to .field {
  transform: translateX(100%);
  opacity: 0;
}
.msg-feedback .delayed-enter-active,
.msg-feedback .delayed-leave-active {
  transition: all 0.35s;
  display: none;
}
</style><style type="text/css">.msg-img {
  max-width: 100%;
}
</style><style type="text/css">.msg-card {
  position: relative;
  padding: 10px;
}
.msg-card.collapsed {
  padding-bottom: calc(42px + 1em);
}
.msg-card .msg-text {
  overflow: hidden;
  max-height: 6em;
  transition: max-height 0.5s ease-in-out;
}
.msg-card .msg-text p,
.msg-card .msg-text br {
  margin-top: 0px;
  margin-bottom: 1em;
  line-height: 1em;
  vertical-align: bottom;
}
.msg-card .read-more {
  position: absolute;
  bottom: 0;
  padding: 22px 0;
  width: calc(100% - 20px);
  text-align: left !important;
}
</style><style type="text/css">.msg-footer {
  font-size: 0.85714286rem;
  font-weight: 400;
  margin-bottom: 1em;
}
.msg-footer p {
  margin: 0;
}
</style><style type="text/css">.msg-header {
  margin: 8px 0;
}
</style><style type="text/css">.msg-hint {
  margin-top: 2em;
}
.msg-hint svg {
  width: 1.3em;
  height: 1.3em;
  min-width: 1.3em;
  min-height: 1.3em;
  margin-right: 0.25em;
  align-self: flex-start;
}
.msg-hint p {
  margin: 0;
  font-size: 1rem;
}
</style><style type="text/css">.warning-pane .msg-warning span {
  display: inline-block;
  padding: 6px 8px;
}
.warning-pane .svg-wrap,
.warning-pane .text-wrap {
  vertical-align: middle;
  display: inline-block;
}
.warning-pane .svg-wrap {
  margin-right: 10px;
}
</style><style type="text/css">.yt_player {
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  position: relative;
}
.yt_player iframe {
  border: 0;
}
.yt_player__iframe-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
</style><style type="text/css">#input {
  padding: 8px 0;
  padding-left: 8px;
}
#input ._field {
  border: 0;
}
#input .listening {
  border-color: #2196f3;
  color: #2196f3;
}
#input .listening ._label {
  -webkit-animation: oscillate 1.5s infinite;
  animation: oscillate 1.5s infinite;
}
#input .listening svg {
  fill: #2196f3;
}
#input ._btn.link {
  padding: 0 0.5em;
}
@-webkit-keyframes oscillate {
0% {
    opacity: .5;
}
50% {
    opacity: 1;
}
100% {
    opacity: .5;
}
}
@keyframes oscillate {
0% {
    opacity: .5;
}
50% {
    opacity: 1;
}
100% {
    opacity: .5;
}
}
</style><style type="text/css">._1PYoAWjz3NQ_VBToxf8PZo {
  width: 100% !important;
  max-width: 100% !important;
  background: #FAFAFA;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.Fy46lWST0KRLpE6z2Eulf {
  flex: 1;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  overflow-y: auto;
  position: relative;
  width: 100%;
}
._1dzV3JdAJS1PFv2AOmyUNm {
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}
._1B2TNG38nJKz1mxZT6XIkO {
  overflow: hidden;
  max-height: 100% !important;
}
._2dRlGQ7cfpPvtU92KUxAc2 {
  overflow-y: auto !important;
}
.r801AySxNjnYTi_vaOGaw {
  width: calc(100% + 40px);
  min-height: 250px;
  height: 250px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
}
.r801AySxNjnYTi_vaOGaw ._2rnmAntCJBVI_Mo1lBp8zU {
  text-align: center;
  width: 100%;
}
.r801AySxNjnYTi_vaOGaw ._24bkC10hK5QNvT1rrxyFnv {
  position: relative;
  width: 80px;
  height: 80px;
  padding: 20px;
  margin: 0 auto;
}
.r801AySxNjnYTi_vaOGaw ._24bkC10hK5QNvT1rrxyFnv svg {
  height: 100%;
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}
.r801AySxNjnYTi_vaOGaw ._24bkC10hK5QNvT1rrxyFnv ._3bF3wTSkFhbqiehsH6_twA {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 40px;
  border-width: 1px;
  border-style: solid;
  -webkit-animation: aa5X33OdByicdZ--BZnNo 2s infinite;
  animation: _1tBAZXrMTH1zB5I6VTRG-y 2s infinite;
}
.r801AySxNjnYTi_vaOGaw ._24bkC10hK5QNvT1rrxyFnv ._2Vv2VHah2ZRxKmfRFvNIXg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 40px;
  -webkit-animation: _3NdXUrYePZffX6c2TQBFr 2s infinite;
  animation: _1RUtNEkUgnRgLo5pkry8Q5 2s infinite;
  transform-origin: center;
}
@keyframes _1tBAZXrMTH1zB5I6VTRG-y {
0% {
    transform: rotate(0deg) skew(0, 0);
}
50% {
    transform: rotate(180deg) skew(0, 5deg);
}
100% {
    transform: rotate(360deg) skew(0, 0);
}
}
@-webkit-keyframes _1tBAZXrMTH1zB5I6VTRG-y {
0% {
    -webkit-transform: rotate(0deg) skew(0, 0);
}
50% {
    -webkit-transform: rotate(180deg) skew(0, 5deg);
}
100% {
    -webkit-transform: rotate(360deg) skew(0, 0);
}
}
@keyframes _1RUtNEkUgnRgLo5pkry8Q5 {
0% {
    transform: skew(0, 0);
}
50% {
    transform: scale(0.65, 0.65) skew(10deg, 17deg);
}
80% {
    transform: scale(1, 1);
}
100% {
    transform: skew(0, 0);
}
}
@-webkit-keyframes _1RUtNEkUgnRgLo5pkry8Q5 {
0% {
    -webkit-transform: skew(0, 0);
}
50% {
    -webkit-transform: scale(0.65, 0.65) skew(10deg, 17deg);
}
80% {
    -webkit-transform: scale(1, 1);
}
100% {
    -webkit-transform: skew(0, 0);
}
}
.ibot-ribbon {
  width: 100%;
  cursor: pointer;
}
.exchangeContainer {
  height: 100%;
  overflow: hidden;
}
.exchangeContainer__fyi {
  background: #f7f7fb;
}
.exchangeContainer__fyi .exchange {
  padding-right: 0!important;
  flex: 1;
}
.poweredByGfis {
  display: flex;
  align-items: center;
  text-transform: uppercase;
  cursor: pointer;
}
.poweredByGfis span {
  margin-right: 8px;
}
.poweredByGfis .poweredByGfis_light {
  display: block;
}
.poweredByGfis .poweredByGfis_dark {
  display: none;
}
.ibot-dark .exchangeContainer__fyi {
  background: #545454;
}
.ibot-dark .poweredByGfis_light {
  display: none;
}
.ibot-dark .poweredByGfis_dark {
  display: block;
}
._fo {
  box-shadow: none !important;
}
._col.scroll {
  -webkit-transition: transform 0.5s ease-out;
  transition: transform 0.5s ease-out;
}
.slide-up-enter-active, .slide-up-leave-active {
  transition: transform 0.5s;
}
.slide-up-enter {
  visibility: hidden;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
}
.slide-up-leave-to {
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
}
.slide-left-delayed-enter-active {
  transition: opacity 0.5s 1s;
}
.slide-left-delayed-enter {
  visibility: hidden;
  opacity: 0;
}
.slide-left-delayed-leave-to {
  opacity: 1;
}
._8XVzGCQgkofJ8VIk3OzpM {
  color: #4d6e9d;
}
._8XVzGCQgkofJ8VIk3OzpM svg {
  fill: #4d6e9d;
  vertical-align: middle;
  width: 20px;
  height: 20px;
}
</style><style type="text/css">.inbu2eI2QbUJISfJHUA5E {
  width: 100%;
  position: relative;
  padding: 14px 0;
}
.inbu2eI2QbUJISfJHUA5E ._3K92hyhXffTTGHptgHMtd2 {
  color: white !important;
}
.inbu2eI2QbUJISfJHUA5E ._3K92hyhXffTTGHptgHMtd2 svg {
  width: 16px;
  height: 16px;
  vertical-align: middle;
}
.inbu2eI2QbUJISfJHUA5E ._23HHVDHr59g1i9RlmZdJM2 {
  position: absolute;
  left: 12px;
  top: 5px;
  bottom: 0;
}
.inbu2eI2QbUJISfJHUA5E .EbHy1HFnz00lpNIgrmrAb {
  text-align: center;
  width: 100%;
}
.inbu2eI2QbUJISfJHUA5E .EbHy1HFnz00lpNIgrmrAb span {
  margin-left: 5px;
}
.inbu2eI2QbUJISfJHUA5E ._29wytHppwVbQa3xhiQndZW {
  position: absolute;
  right: 12px;
  top: 5px;
  bottom: 0;
}
</style><style type="text/css">.blurred-body > *:not(#ibot-div) {
  filter: blur(3px);
}
body.dialog-open {
  overflow-y: hidden;
}
body ._Ts9v_O_g-AyXJZVK2P6o {
  position: fixed;
  top: auto;
  right: 0;
  left: auto;
  bottom: 0;
  z-index: 1020;
}
body ._Ts9v_O_g-AyXJZVK2P6o.YjgEfcoS205_eEV5CFy9A {
  z-index: 16384;
  top: 0;
  left: 0;
}
@media only screen and (min-width: 769px) {
body ._Ts9v_O_g-AyXJZVK2P6o.YjgEfcoS205_eEV5CFy9A {
    top: auto;
    left: auto;
}
}
body .pBqL3UYwaZQ3Q2A4TxNVN {
  position: relative;
  max-width: 100%;
  max-height: calc(100% - 48px);
  z-index: 16385;
  transform-origin: bottom right;
  height: 100%;
}
body .Lwclm_FBre1J88Lm0Z1s5 {
  position: absolute;
  left: -10000px;
  top: -10000px;
}
@media only screen and (min-width: 769px) {
body .pBqL3UYwaZQ3Q2A4TxNVN {
    max-height: 720px;
    height: calc(100vh - 192px);
    min-height: 500px;
    max-width: 400px;
    width: 400px;
    margin-right: 32px;
    margin-bottom: 72px;
}
}
body ._1PZ37C9oIgKsi0kAZ2bquk {
  -webkit-box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);
  box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);
}
@media only screen and (min-width: 769px) {
body ._1PZ37C9oIgKsi0kAZ2bquk {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}
}
body ._2p0nRWFaCuJ21cRu-5eQGW {
  -webkit-box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}
@media only screen and (min-width: 769px) {
body ._2p0nRWFaCuJ21cRu-5eQGW {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
body ._2p0nRWFaCuJ21cRu-5eQGW #input {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
}
body .LClnz62Y6F4TGY7NOnTtg {
  position: absolute;
  right: 32px;
  bottom: 32px;
  z-index: 1025;
  transform-origin: bottom right;
}
body ._3y7gz5ImPNLQmfoQaljBcw > svg {
  background: #fff;
  border-radius: 50%;
}
body ._2LhcnWUklpGEnLvDCAM1lL {
  margin: 0;
  padding: 0 12px;
  max-width: none;
  border-radius: 6px 6px 6px 0;
  box-shadow: 0 16px 22px rgba(0, 0, 0, 0.06), 0 2px 6px 1px rgba(0, 0, 0, 0.07), 0 9px 22px 6px rgba(0, 0, 0, 0.07);
}
body ._2LhcnWUklpGEnLvDCAM1lL > .mE1Y3-eeDfkcuSw7qa2Cz {
  position: absolute;
  bottom: -13px;
  left: -1px;
  border-width: 13px;
  border-style: solid;
  border-left: 0;
  border-bottom: 0;
  border-right-color: transparent !important;
}
body ._2LhcnWUklpGEnLvDCAM1lL:focus > span,
body ._2LhcnWUklpGEnLvDCAM1lL:hover > span {
  border-top-color: #476490;
}
body ._3GcF36olIWWE4rOsc_fIr4 {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 5;
}
body .ibot-dark .ibot-avatar:hover > span {
  border-top-color: #439cff;
}
body .togglebot-enter-active,
body .togglebot-leave-active {
  transition: all 0.5s;
}
body .togglebot-enter {
  opacity: 0;
  transform: translateY(200px);
}
body .togglebot-leave-to {
  opacity: 0;
  transform: translateY(200px);
}
body .ibot-a {
  fill: #fff;
  stroke: #fff;
  strokewidth: 3px;
}
body .ibot-b {
  fill: #d81222;
}
</style><script id="reportingAdviewer" src="https://www.interactivebrokers.co.uk/adviewer/nightly/umd1/index.js?v=31612?_=2025041612" type="text/javascript"></script><style type="text/css">.infoViewer{background:transparent;cursor:pointer;overflow:hidden;width:100%}.infoViewer img{width:100%}.infoViewer__placeholder{align-items:center;background:rgba(77,110,157,.3);display:flex;flex-direction:column;font-family:sans-serif;height:100%;justify-content:center;width:100%}.infoViewer__placeholder--message{font-size:14px;margin-top:0}.infoViewer__placeholder h2{margin-top:0}.infoViewer__placeholder--rev{font-family:sans-serif;font-size:.2em;margin:3px 0 0;opacity:.3}</style><style type="text/css">.myapp .thing{opacity:.5}</style></head>
    <body style="padding: 163.484px 0px 0px;">
        <!---->
        
        
            
                <div dir="ltr" class="_app _con _acap _awidget"><!----><main id="_ib_main_50" class="ib-cap"><!----><!----><div class="_bar _ptop _e3"><div class="_plin ib-bar3 _ptop" style="height: 100%;"><div role="progressbar" class="_plinbar" style="display: none;"><div class="_plinbg"></div><div class="_plinfg _inf" style="transition-duration: 500ms;"></div></div><div class="one-head"><div class="one-head-inner fs7 no-select no-wrap"><!----><!----><div class="one-head-mkt"><div class="one-head-items"><a role="button" tabindex="0" class="one-head-item bar-fg one-head-32"><!---->S&amp;P 500
 <!----><span><span class="text-bold numeric">5396.70</span></span> <span class="text-bold numeric">
0.00%</span></a><a role="button" tabindex="0" class="one-head-item bar-fg one-head-32"><!---->NASDAQ Comp
 <!----><span><span class="text-bold numeric">C16823.17</span></span> <span class="text-bold numeric">
—</span></a><a role="button" tabindex="0" class="one-head-item bar-fg one-head-32"><!---->RUSSELL 1000
 <!----><span><span class="text-bold numeric">2945.67</span></span> <span class="text-bold numeric">
0.00%</span></a></div><button type="button" class="_btn link bar-fg insetx-6" aria-expanded="false" aria-label="Choose indexes"><svg viewBox="0 0 24 24"><path d="M5 10a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"></path></svg><!----></button></div><div class="one-head-menu"><!----><!----><button type="button" class="_btn fs7 link bar-fg inset-4" aria-expanded="false">Get Help <svg viewBox="0 0 50 50"><path d="M25 2C12.32 2 2 12.32 2 25s10.32 23 23 23 23-10.32 23-23S37.68 2 25 2zm1.8 34.94c0 .21-.15.35-.36.35H23.7c-.21 0-.36-.14-.36-.35v-3.09c0-.21.15-.36.36-.36h2.73c.21 0 .36.15.36.36v3.09zm3.15-13.67l-2.4 3.3a3.7 3.7 0 0 0-.93 2.69v1.14c0 .22-.15.36-.36.36h-2.37c-.21 0-.36-.14-.36-.35v-1.47c0-1.44.33-2.2 1.08-3.23l2.4-3.3c1.26-1.72 1.69-2.55 1.69-3.77 0-2.04-1.44-3.33-3.48-3.33-2.01 0-3.3 1.22-3.73 3.4-.04.22-.18.33-.4.3l-2.26-.4c-.21-.04-.32-.18-.28-.4.54-3.44 3.01-5.7 6.74-5.7 3.87 0 6.49 2.54 6.49 6.1 0 1.72-.6 2.97-1.83 4.66z"></path></svg><!----></button><section class="_con"><div class="feedbackApp"><div desc=""><div><button type="button" class="_btn fs7 link bar-fg inset-4"><svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30"><rect width="30" height="30" fill="none"></rect><g transform="translate(-581 356)"><path d="M481.4-110.009a7.681,7.681,0,0,1-4.414-1.5A8.977,8.977,0,0,1,474-111a8.555,8.555,0,0,1-5.669-2.062,13.387,13.387,0,0,0,7.509-3.139,11.831,11.831,0,0,0,3.949-6.631A6.533,6.533,0,0,1,482-118a6.531,6.531,0,0,1-2.179,4.8,5.879,5.879,0,0,0,1.853,2.229.5.5,0,0,1,.326.468.5.5,0,0,1-.5.5A.449.449,0,0,1,481.4-110.009ZM457-114.5a.5.5,0,0,1,.326-.469,5.625,5.625,0,0,0,2.072-2.8,10,10,0,0,1-2.484-3.23,9.159,9.159,0,0,1-.914-4c0-5.514,4.934-10,11-10s11,4.486,11,10a9.5,9.5,0,0,1-3.222,7.071A11.508,11.508,0,0,1,467-115a11.859,11.859,0,0,1-4.5-.871,7.91,7.91,0,0,1-4.9,1.861.553.553,0,0,1-.1.01A.5.5,0,0,1,457-114.5Zm6-7.269h.769a2.645,2.645,0,0,0,1.858.77h3.912a1.159,1.159,0,0,0,1.129-.916l0-.009.766-3.832a.143.143,0,0,1,0-.018l.009-.042h0l0,0a1.106,1.106,0,0,0,.018-.178,1.155,1.155,0,0,0-1.154-1.154h-3.653a16.572,16.572,0,0,0,.384-2.308A1.413,1.413,0,0,0,465.71-131h-.4v1.049l-.718,2.371a1.536,1.536,0,0,1-1.355.81H463Z" transform="translate(127 -218)"></path></g></svg><!----></button></div><!----></div></div></section><button type="button" class="_btn fs7 link bar-fg inset-4"><svg viewBox="0 0 24 24"><path d="M20 4H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm0 4l-8 5-8-5V6l8 5 8-5z"></path></svg><!----></button><button type="button" class="_btn fs7 link bar-fg inset-4" aria-expanded="false"><svg viewBox="-1279 -15032 17.882 19" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false"><g transform="translate(-1283 -15036)"><path d="M12.941,4A4.455,4.455,0,0,0,8.471,8.925c.044.607.306,1.8.349,2.41v.035c.349,1.694,2.279,2.689,4.121,2.689a4.479,4.479,0,0,0,4.121-2.689c0-.009,0-.026,0-.035a15.949,15.949,0,0,0,.349-2.41,3.535,3.535,0,0,0-2.9-4.017L14.059,4Zm0,12.294C6.148,16.294,4,20.765,4,20.765V23H21.882V20.765S19.734,16.294,12.941,16.294Z"></path></g></svg><!----></button></div></div><!----><!----></div><div class="nav-container insetx-32 insety-16"><div class="bar3-logo before-0 after-4 end-64"><a href="javascript:void(0);" class="fg bar3-logo__ib cursor a__inherit-color"><!----><!----><svg version="1.1" viewBox="0 0 452 76"><path d="M56.1 65.4h-7.2V30.5h7.2v34.9zM83.2 41.1c1.7 1.4 2.5 3.7 2.5 6.9v17.4h-6.9V49.7c0-1.4-.2-2.4-.5-3.1-.7-1.3-1.9-2-3.8-2-2.3 0-3.8 1-4.7 2.9-.4 1-.7 2.3-.7 3.9v14h-6.7V39.7h6.5v3.8c.9-1.3 1.7-2.3 2.4-2.9 1.4-1 3.1-1.6 5.3-1.6 2.7 0 4.9.7 6.6 2.1zM88.9 44.7v-4.8h3.6v-7.2h6.7v7.2h4.2v4.8h-4.2v13.6c0 1.1.1 1.7.4 2s1.1.4 2.5.4h1.4v5l-3.2.1c-3.2.1-5.4-.4-6.5-1.7-.8-.8-1.1-2-1.1-3.6V44.7h-3.8zM124.1 40.2c1.8.8 3.3 2.1 4.5 3.8 1.1 1.5 1.7 3.3 2.1 5.3.2 1.2.3 2.9.2 5.1h-18.8c.1 2.6 1 4.4 2.7 5.4 1 .6 2.3 1 3.7 1 1.5 0 2.8-.4 3.7-1.2.5-.4 1-1 1.4-1.8h6.9c-.2 1.5-1 3.1-2.5 4.7-2.3 2.5-5.5 3.8-9.7 3.8-3.4 0-6.5-1.1-9.1-3.2-2.6-2.1-3.9-5.6-3.9-10.3 0-4.5 1.2-7.9 3.6-10.3 2.4-2.4 5.4-3.6 9.2-3.6 2.2 0 4.2.5 6 1.3zm-10 5.8c-1 1-1.6 2.3-1.8 4h11.6c-.1-1.8-.7-3.1-1.8-4.1-1.1-.9-2.4-1.4-4-1.4-1.8 0-3.1.5-4 1.5zM149.1 39h.6v7c-.4 0-.8-.1-1.1-.1h-.8c-2.7 0-4.5.9-5.5 2.6-.5 1-.8 2.5-.8 4.6v12.3h-6.8V39.6h6.4v4.5c1-1.7 1.9-2.9 2.7-3.5 1.3-1.1 2.9-1.6 4.9-1.6h.4zM163.8 49.7c1.3-.2 2.2-.4 2.7-.6 1-.4 1.5-1 1.5-1.9 0-1.1-.4-1.8-1.1-2.2-.7-.4-1.8-.6-3.3-.6-1.6 0-2.8.4-3.4 1.2-.5.6-.8 1.4-1 2.4h-6.5c.1-2.3.8-4.1 1.9-5.6 1.8-2.3 4.9-3.4 9.2-3.4 2.8 0 5.4.6 7.6 1.7 2.2 1.1 3.3 3.2 3.3 6.3v14.8c0 .9.2 1.5.4 1.8.2.3.6.6 1 .8v1h-7.3c-.2-.5-.3-1-.4-1.5s-.1-1-.2-1.6c-.9 1-2 1.9-3.2 2.6-1.5.8-3.1 1.3-5 1.3-2.3 0-4.3-.7-5.8-2s-2.3-3.2-2.3-5.7c0-3.2 1.2-5.5 3.7-6.9 1.4-.8 3.3-1.3 6-1.7l2.2-.2zm4.1 3.2c-.4.3-.9.5-1.3.7-.4.2-1 .3-1.8.5l-1.5.3c-1.4.3-2.5.6-3.1.9-1.1.6-1.6 1.6-1.6 2.9 0 1.2.3 2 1 2.5.6.5 1.4.8 2.4.8 1.5 0 2.8-.4 4.1-1.3 1.2-.9 1.9-2.4 1.9-4.7v-2.6zM195.9 48.9c-.1-1-.4-1.8-1-2.6-.8-1-1.9-1.6-3.5-1.6-2.3 0-3.8 1.1-4.7 3.4-.4 1.2-.7 2.8-.7 4.8 0 1.9.2 3.4.7 4.6.8 2.1 2.3 3.2 4.6 3.2 1.6 0 2.7-.4 3.4-1.3.7-.9 1.1-2 1.2-3.3h6.9c-.2 2.1-.9 4-2.2 5.8-2.1 2.9-5.2 4.4-9.4 4.4s-7.2-1.2-9.2-3.7-3-5.6-3-9.6c0-4.4 1.1-7.9 3.2-10.3 2.2-2.5 5.1-3.7 9-3.7 3.2 0 5.9.7 7.9 2.2s3.3 4 3.7 7.7h-6.9zM205 44.7v-4.8h3.6v-7.2h6.7v7.2h4.2v4.8h-4.2v13.6c0 1.1.1 1.7.4 2s1.1.4 2.5.4h1.4v5l-3.2.1c-3.2.1-5.4-.4-6.5-1.7-.8-.8-1.1-2-1.1-3.6V44.7H205zM230.7 36.6h-6.8v-6.2h6.8v6.2zm-6.8 3h6.8v25.8h-6.8V39.6zM252.6 39.6h7.2l-9.3 25.8h-7.1l-9.2-25.8h7.6l5.4 19 5.4-19zM279.6 40.2c1.8.8 3.3 2.1 4.5 3.8 1.1 1.5 1.7 3.3 2.1 5.3.2 1.2.3 2.9.2 5.1h-18.8c.1 2.6 1 4.4 2.7 5.4 1 .6 2.3 1 3.7 1 1.5 0 2.8-.4 3.7-1.2.5-.4 1-1 1.4-1.8h6.9c-.2 1.5-1 3.1-2.5 4.7-2.3 2.5-5.5 3.8-9.7 3.8-3.4 0-6.5-1.1-9.1-3.2-2.6-2.1-3.9-5.6-3.9-10.3 0-4.5 1.2-7.9 3.6-10.3 2.4-2.4 5.4-3.6 9.2-3.6 2.1 0 4.2.5 6 1.3zM269.5 46c-1 1-1.6 2.3-1.8 4h11.6c-.1-1.8-.7-3.1-1.8-4.1-1.1-.9-2.4-1.4-4-1.4-1.7 0-3.1.5-4 1.5zM290.8 30.7h15c4.1 0 7 1.2 8.7 3.6 1 1.4 1.5 3.1 1.5 5 0 2.2-.6 4-1.9 5.4-.6.7-1.6 1.4-2.8 2 1.8.7 3.1 1.4 4 2.3 1.6 1.5 2.3 3.6 2.3 6.3 0 2.2-.7 4.3-2.1 6.1-2.1 2.7-5.5 4.1-10.1 4.1h-14.8V30.7zm13.3 14.7c2 0 3.6-.3 4.7-.8 1.8-.9 2.6-2.4 2.6-4.7 0-2.3-.9-3.8-2.8-4.6-1.1-.4-2.6-.7-4.7-.7h-8.5v10.8h8.7zm1.5 16c2.9 0 5-.8 6.2-2.5.8-1.1 1.2-2.3 1.2-3.8 0-2.5-1.1-4.2-3.4-5.2-1.2-.5-2.8-.7-4.8-.7h-9.4v12.2h10.2zM322.7 40.1h4v4.4c.3-.9 1.1-1.9 2.4-3.1 1.3-1.2 2.8-1.8 4.5-1.8h.4c.2 0 .5 0 1 .1v4.5c-.3 0-.5-.1-.7-.1h-.7c-2.1 0-3.8.7-4.9 2.1-1.2 1.4-1.7 3-1.7 4.8v14.6h-4.3V40.1zM356.8 42.6c2.2 2.2 3.4 5.4 3.4 9.6 0 4.1-1 7.5-3 10.1-2 2.7-5 4-9.2 4-3.5 0-6.2-1.2-8.2-3.5-2-2.4-3.1-5.5-3.1-9.5 0-4.3 1.1-7.6 3.2-10.2 2.1-2.5 5-3.8 8.6-3.8 3.3.1 6.1 1.1 8.3 3.3zm-2.6 16.9c1-2.1 1.6-4.5 1.6-7.1 0-2.4-.4-4.3-1.1-5.8-1.2-2.3-3.2-3.5-6.1-3.5-2.6 0-4.4 1-5.6 3-1.2 2-1.8 4.4-1.8 7.2 0 2.7.6 4.9 1.8 6.7 1.2 1.8 3 2.7 5.6 2.7 2.6 0 4.5-1.1 5.6-3.2zM365 30.7h4.1v20.2L380 40.1h5.4l-9.7 9.4L386 65.4h-5.4l-8-12.9-3.5 3.4v9.5H365V30.7zM403.7 40.8c1.7.8 3 1.9 3.8 3.3.8 1.3 1.4 2.8 1.7 4.5.3 1.2.4 3 .4 5.6h-18.4c.1 2.6.7 4.6 1.8 6.2 1.1 1.6 2.9 2.3 5.2 2.3 2.2 0 4-.7 5.3-2.2.7-.9 1.3-1.8 1.6-3h4.2c-.1.9-.5 2-1.1 3.1-.6 1.1-1.3 2.1-2.1 2.8-1.3 1.3-2.9 2.1-4.8 2.6-1 .3-2.2.4-3.5.4-3.1 0-5.8-1.1-8-3.4s-3.3-5.5-3.3-9.6 1.1-7.4 3.3-9.9c2.2-2.5 5.1-3.8 8.6-3.8 1.9-.2 3.6.2 5.3 1.1zm1.5 9.9c-.2-1.8-.6-3.3-1.2-4.4-1.2-2.1-3.1-3.1-5.8-3.1-1.9 0-3.6.7-4.9 2.1-1.3 1.4-2 3.2-2.1 5.4h14zM414.6 40.1h4v4.4c.3-.9 1.1-1.9 2.4-3.1 1.3-1.2 2.8-1.8 4.5-1.8h.4c.2 0 .5 0 1 .1v4.5c-.3 0-.5-.1-.7-.1h-.7c-2.1 0-3.8.7-4.9 2.1-1.2 1.4-1.7 3-1.7 4.8v14.6h-4.3V40.1zM433.2 57.5c.1 1.4.5 2.5 1.1 3.3 1.1 1.4 3 2.1 5.7 2.1 1.6 0 3-.3 4.3-1 1.2-.7 1.8-1.7 1.8-3.2 0-1.1-.5-1.9-1.5-2.5-.6-.3-1.9-.7-3.7-1.2l-3.4-.9c-2.2-.5-3.8-1.1-4.9-1.8-1.9-1.2-2.8-2.7-2.8-4.8 0-2.4.9-4.3 2.6-5.8 1.7-1.5 4.1-2.2 7.1-2.2 3.9 0 6.7 1.1 8.4 3.4 1.1 1.4 1.6 3 1.6 4.6h-4c-.1-1-.4-1.8-1-2.6-1-1.1-2.7-1.7-5.2-1.7-1.6 0-2.9.3-3.7.9-.8.6-1.3 1.4-1.3 2.4 0 1.1.6 2 1.7 2.6.6.4 1.6.7 2.8 1l2.9.7c3.1.7 5.2 1.5 6.3 2.2 1.7 1.1 2.5 2.8 2.5 5.1s-.9 4.2-2.6 5.8c-1.7 1.6-4.4 2.5-7.9 2.5-3.8 0-6.5-.9-8.1-2.6-1.6-1.7-2.4-3.8-2.6-6.4h3.9z"></path><g><linearGradient id="a_88636e" gradientUnits="userSpaceOnUse" x1="3864.738" y1="49.783" x2="3893.144" y2="49.783" gradientTransform="matrix(-1 0 0 1 3894.245 0)"><stop offset="0" stop-color="#d81222"></stop><stop offset="1" stop-color="#960b1a"></stop></linearGradient><path fill="url(#a_88636e)" d="M29.5 65.4H1.1V34.1z"></path><circle fill="#d81222" cx="25.5" cy="41.8" r="8.5"></circle><path fill="#d81222" d="M29.5 1.2L1.1 34.1v31.3z"></path></g></svg></a><!----></div><!----><div class="ib-bar3__trade-btn-container"><div class="ib-bar3__middle-container"><div class="ib-bar3__sl-outer-container"><div class="sl-search-bar ib-bar3__sl-container"><span class="sl-symbol-input sl-symbol-input-defaults sl-symbol-input-theme-default"><span class="_field sl-input-field fg bg15-gray placeholder"><!----><span class="_fldi">‌<label for="_f65" class="_fldl">Symbol or Site Search</label><input id="_f65" type="text" class="_fldin"></span><!----></span><div class="sl-search-icon mirror-rtl"><span class="screen-reader-text">symbol_look_up</span><svg viewBox="0 0 24 24"><path d="M10 0a10 10 0 106.322 17.736l5.971 5.971a1 1 0 101.414-1.414l-5.971-5.971A9.99 9.99 0 0010 0zm0 2a8 8 0 11-8 8 7.985 7.985 0 018-8z"></path></svg></div></span></div></div></div><div class="flex-flex middle"><span class="_bdg outsetx-16"><button type="button" class="_btn fs3 link" aria-label="Notifications"><svg viewBox="0 0 24 24"><path d="M12 2c-.8 0-1.5.7-1.5 1.5v.69A6.94 6.94 0 0 0 5 11v4.5l-2 2.31V19h18v-1.19l-2-2.31V11a6.94 6.94 0 0 0-5.5-6.81V3.5c0-.8-.7-1.5-1.5-1.5zm-2 18a2 2 0 0 0 4 0z"></path></svg><!----></button><span aria-atomic="true" aria-label="Badge" aria-live="polite" role="status" class="_bdgw" style="top: -5px; left: calc(100% - 12px);">2</span></span><button type="button" id="homepage-trade-btn" class="_btn accent lg" aria-label="Trade">Trade<!----></button></div></div></div><!----><div class="nav-items__next-row insetx-32 insety-12"><div class="nav-items-container"><!----><div><button type="button" class="_btn link clear nav-item fg">Portfolio<!----><!----></button></div><div><button type="button" class="_btn link clear nav-item fg" aria-expanded="false" aria-label="Trade">Trade<!----><!----></button></div><div><button type="button" class="_btn link clear nav-item fg" aria-expanded="false" aria-label="Research">Research<!----><!----></button></div><div><button type="button" class="_btn link clear nav-item fg" aria-expanded="false" aria-label="Transfer &amp; Pay">Transfer &amp; Pay<!----><!----></button></div><div><button type="button" class="_btn link clear nav-item fg" aria-expanded="false" aria-label="Performance &amp; Reports">Performance &amp; Reports<!----><!----></button></div><div><button type="button" class="_btn link clear nav-item fg" aria-expanded="false" aria-label="Education">Education<!----><!----></button></div><!----></div></div><!----><!----></div></div><!----><!----><!----><!----><!----><!----></main><!----><div tabindex="0" aria-hidden="true" class="_dlg _dlg-modal" style="display: none;"><div role="dialog" aria-modal="true" tabindex="0" class="_dlgo"><!----><div class="_dlgi"><div><div class="ib-row"><div class="ib-col inset-16"><h4 class="after-16">Bid, Ask, and Last Size Display Update</h4><p></p><span class="_check before-16"><input type="checkbox" id="_chk75" value=""><span aria-hidden="true" class="_cbc">‌</span><label for="_chk75" class="_cbl">Do not show this message again</label></span></div></div><div class="ib-row"><div class="ib-col border-top inset-16 flex-fixed"><button type="button" class="_btn link text-left">Learn More<!----></button><button type="button" class="_btn">Dismiss<!----></button></div></div></div></div><!----></div></div><div tabindex="0" aria-hidden="true" class="_dlg _dlg-modal" style="display: none;"><div role="dialog" aria-modal="true" tabindex="0" class="_dlgo bg-canvas" style="width: 650px; height: auto;"><div class="_dlgh"><div class="fixed-fixed middle insety-16 w100"><div class="flex-flex middle fg"><i class="icon-32"><svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30"><rect width="30" height="30" fill="none"></rect><g transform="translate(-581 356)"><path d="M481.4-110.009a7.681,7.681,0,0,1-4.414-1.5A8.977,8.977,0,0,1,474-111a8.555,8.555,0,0,1-5.669-2.062,13.387,13.387,0,0,0,7.509-3.139,11.831,11.831,0,0,0,3.949-6.631A6.533,6.533,0,0,1,482-118a6.531,6.531,0,0,1-2.179,4.8,5.879,5.879,0,0,0,1.853,2.229.5.5,0,0,1,.326.468.5.5,0,0,1-.5.5A.449.449,0,0,1,481.4-110.009ZM457-114.5a.5.5,0,0,1,.326-.469,5.625,5.625,0,0,0,2.072-2.8,10,10,0,0,1-2.484-3.23,9.159,9.159,0,0,1-.914-4c0-5.514,4.934-10,11-10s11,4.486,11,10a9.5,9.5,0,0,1-3.222,7.071A11.508,11.508,0,0,1,467-115a11.859,11.859,0,0,1-4.5-.871,7.91,7.91,0,0,1-4.9,1.861.553.553,0,0,1-.1.01A.5.5,0,0,1,457-114.5Zm6-7.269h.769a2.645,2.645,0,0,0,1.858.77h3.912a1.159,1.159,0,0,0,1.129-.916l0-.009.766-3.832a.143.143,0,0,1,0-.018l.009-.042h0l0,0a1.106,1.106,0,0,0,.018-.178,1.155,1.155,0,0,0-1.154-1.154h-3.653a16.572,16.572,0,0,0,.384-2.308A1.413,1.413,0,0,0,465.71-131h-.4v1.049l-.718,2.371a1.536,1.536,0,0,1-1.355.81H463Z" transform="translate(127 -218)"></path></g></svg></i><h2 class="text-semibold fs5 start-8">Feedback &amp; Suggestions</h2></div><button type="button" class="_btn fs4 fg70 outset-0 inset-0 clear" aria-label="Close Feedback Modal"><svg viewBox="0 0 50 50"><path d="M9.16 6.31L6.3 9.16 22.16 25 6.22 40.97l2.81 2.81L25 27.84l15.94 15.94 2.84-2.84L27.84 25 43.7 9.16 40.84 6.3 25 22.16z"></path></svg><!----></button></div></div><div class="_dlgi"><div class="inset-16 w100"><div class="feedbackAppGeneralFB"><p class="after-16">We would love to hear your thoughts.</p><div class="feedbackAppGeneralFB__btnContainer"><button type="button" class="_btn feedbackAppGeneralFB__btn outsetx-0 inset-16 bg10-accent fg-accent"><i class="icon-32"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g id="Group_10340" data-name="Group 10340" transform="translate(-51 -15)"><g id="Rectangle_18561" data-name="Rectangle 18561" transform="translate(51 15)" fill="none" stroke-width="1" opacity="0"><rect width="32" height="32" stroke="none"></rect> <rect x="0.5" y="0.5" width="31" height="31" fill="none"></rect></g> <g id="Group_10092" data-name="Group 10092" transform="translate(54.019 18.001)"><path id="Path_13242" data-name="Path 13242" d="M12,25v1a2,2,0,0,0,2,2,1,1,0,0,0,2,0,2,2,0,0,0,2-2V25Z" transform="translate(-2 -2.516)"></path> <path id="Path_13243" data-name="Path 13243" d="M6.529,2.516a1,1,0,1,0,.693.293A1,1,0,0,0,6.529,2.516Zm16.971,0a1,1,0,1,0,.691.293,1,1,0,0,0-.691-.293ZM15,3a9,9,0,0,0-9,9c0,5,4,7,6,11h6c2-4,6-6,6-11a9,9,0,0,0-9-9ZM19,11ZM3,11a1,1,0,1,0,1,1A1,1,0,0,0,3,11Zm24,0a1,1,0,1,0,1,1A1,1,0,0,0,27,11ZM6.529,19.484a1,1,0,1,0,.693.293A1,1,0,0,0,6.529,19.484Zm16.969,0a1,1,0,1,0,.693.291,1,1,0,0,0-.693-.291Z" transform="translate(-2 -2.516)"></path></g></g></svg></i><p class="before-8 insetx-16 parent-stylesheet-override">I have a suggestion</p><!----></button><button type="button" class="_btn feedbackAppGeneralFB__btn outsetx-0 inset-16 bg10-accent fg-accent"><i class="icon-32"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g id="Group_10338" data-name="Group 10338" transform="translate(-51 -15)"><g id="Rectangle_18559" data-name="Rectangle 18559" transform="translate(51 15)" fill="none" stroke-width="1" opacity="0"><rect width="32" height="32" stroke="none"></rect> <rect x="0.5" y="0.5" width="31" height="31" fill="none"></rect></g> <path id="Exclusion_2" data-name="Exclusion 2" d="M13,26A13,13,0,0,1,3.807,3.808,13,13,0,1,1,13,26ZM9.442,17.718h0a1.18,1.18,0,0,0-.609,2.145,6.865,6.865,0,0,0,8.331,0,1.182,1.182,0,0,0-.7-2.138,1.169,1.169,0,0,0-.7.229,4.516,4.516,0,0,1-5.555,0,1.148,1.148,0,0,0-.658-.236Zm8.874-7.083a1.773,1.773,0,1,0,1.772,1.775A1.776,1.776,0,0,0,18.316,10.635Zm-10.635,0A1.773,1.773,0,1,0,9.453,12.41,1.776,1.776,0,0,0,7.681,10.635Z" transform="translate(54.019 18.001)"></path></g></svg></i><p class="before-8 insetx-16 parent-stylesheet-override">I like something</p><!----></button><button type="button" class="_btn feedbackAppGeneralFB__btn outsetx-0 inset-16 bg10-accent fg-accent"><i class="icon-32"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g id="Group_10339" data-name="Group 10339" transform="translate(-51 -15)"><g id="Rectangle_18560" data-name="Rectangle 18560" transform="translate(51 15)" fill="none" stroke-width="1" opacity="0"><rect width="32" height="32" stroke="none"></rect> <rect x="0.5" y="0.5" width="31" height="31" fill="none"></rect></g> <g id="Group_10091" data-name="Group 10091" transform="translate(54.019 18)"><path id="Exclusion_3" data-name="Exclusion 3" d="M13,26A13,13,0,0,1,3.807,3.809,13,13,0,0,1,22.192,22.193,12.914,12.914,0,0,1,13,26Zm0-7.094a4.528,4.528,0,0,1,2.777.958,1.171,1.171,0,0,0,.7.227,1.182,1.182,0,0,0,.7-2.138,6.875,6.875,0,0,0-8.339,0,1.182,1.182,0,0,0,.7,2.138,1.171,1.171,0,0,0,.7-.227A4.529,4.529,0,0,1,13,18.907Zm5.315-8.271a1.773,1.773,0,1,0,1.772,1.774A1.776,1.776,0,0,0,18.319,10.637Zm-10.636,0a1.773,1.773,0,1,0,1.772,1.774A1.776,1.776,0,0,0,7.682,10.637Z"></path></g></g></svg></i><p class="before-8 insetx-16 parent-stylesheet-override">I don't like something</p><!----></button><button type="button" class="_btn feedbackAppGeneralFB__btn outsetx-0 inset-16 bg10-accent fg-accent"><i class="icon-32"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g id="Group_10341" data-name="Group 10341" transform="translate(-51 -15)"><g id="Rectangle_18562" data-name="Rectangle 18562" transform="translate(51 15)" fill="none" stroke="#707070" stroke-width="1" opacity="0"><rect width="32" height="32" stroke="none"></rect> <rect x="0.5" y="0.5" width="31" height="31" fill="none"></rect></g> <path id="icons8-bug" d="M10.031,2.9A1,1,0,0,0,9.06,4.258,6.519,6.519,0,0,0,11.488,7.08,3.211,3.211,0,0,0,9.8,8.457,7.653,7.653,0,0,1,8.849,8.1a4.157,4.157,0,0,1-.891-.551C7.8,7.413,7.8,7.406,7.8,7.406A1,1,0,0,0,7,7H5A1,1,0,1,0,5,9H6.6c.041.038.031.039.078.078A6.16,6.16,0,0,0,7.99,9.9,13.805,13.805,0,0,0,13.935,11a1,1,0,0,0,.205-.02c.29.007.557.02.877.02s.565-.011.846-.018a1,1,0,0,0,.2.018,13.805,13.805,0,0,0,5.945-1.1,6.16,6.16,0,0,0,1.316-.824c.047-.039.037-.04.078-.078H25a1,1,0,1,0,0-2H23a1,1,0,0,0-.8.406s.005.007-.154.141a4.157,4.157,0,0,1-.891.551,7.633,7.633,0,0,1-.973.365,3.237,3.237,0,0,0-1.66-1.387,6.52,6.52,0,0,0,2.422-2.818,1,1,0,1,0-1.879-.682,3.655,3.655,0,0,1-2.178,2.1,2.961,2.961,0,0,0-3.765,0,3.655,3.655,0,0,1-2.178-2.1A1,1,0,0,0,10.031,2.9ZM5.963,11a1,1,0,0,0-.279.051l-3,1a1,1,0,0,0,.633,1.9l2.654-.885c.1.047.379.171.621.279A8.341,8.341,0,0,0,6,16.425a10.353,10.353,0,0,0,.795,4.027l-.635,1.9-2.715,1.81a1,1,0,1,0,1.109,1.664l3-2a1,1,0,0,0,.395-.516l.187-.564C9.8,24.9,11.988,26,13,26a.957.957,0,0,0,1-1V16a1,1,0,1,1,2,0v9a.957.957,0,0,0,1,1c1.012,0,3.2-1.1,4.863-3.248l.187.564a1,1,0,0,0,.395.516l3,2a1,1,0,1,0,1.109-1.664l-2.715-1.81-.635-1.9A10.353,10.353,0,0,0,24,16.425a8.341,8.341,0,0,0-.592-3.082c.242-.108.525-.232.621-.279l2.654.885a1,1,0,0,0,.633-1.9l-3-1a1,1,0,0,0-.764.055A21.915,21.915,0,0,1,17.513,12.9c-.774.06-1.6.1-2.514.1s-1.74-.044-2.514-.1A21.915,21.915,0,0,1,6.447,11.1,1,1,0,0,0,5.963,11Z" transform="translate(52.02 15.096)"></path></g></svg></i><p class="before-8 insetx-16 parent-stylesheet-override">I found a bug</p><!----></button></div></div><!----></div></div><div class="_dlgf"><div class="inset-12 feedbackAppFull__btm text-end"><button type="button" class="_btn outset-4 insetx-32"><span>Cancel</span><!----></button><button type="button" disabled="disabled" class="_btn accent outset-4 insetx-32"><span>Send Feedback</span><!----></button></div></div></div></div></div>
                <am-bar-page-header><div id="amAppHeader" class="ib-bar-spacing am-app-header">
    <am-page-header><!----><section ng-if="!$ctrl.isHideHeader()" class="page-head">    <div class="container">        <div class="row">            <div class="col-8 col-sm-10 col-md-10 col-lg-10">                <h3>                    <!---->                    Statements                    <span class="d-block d-sm-none"><br></span>                    <span class="account-numbers">                        <!----><a href="javascript:void(0);" class="btn btn-primary" ng-click="$ctrl.openPicker()" ng-repeat="account in $ctrl.getPickerSelections() track by $index" ng-class="{ 'disabled' : !$ctrl.isPickerAvailable() }">*********</a><!---->                        <!----><a href="javascript:void(0);" class="btn btn-secondary" ng-click="$ctrl.openPicker()" ng-if="$ctrl.displayPickerEdit()">Select Account(s) </a><!---->                    </span>                    <!---->                </h3>            </div>            <div class="col-4 col-sm-2 col-md-2 col-lg-2">                <span class="btn-group-right">                    <ul class="btn-icons">                       <!---->                       <!----><li ng-if="$ctrl.isPrintSupported()">                           <!----><am-button ng-if="$ctrl.isPrintSupported()" on-click="$ctrl.print()" btn-type="icon" btn-icon="fa-print" tooltip-text="Print"><a role="button" aria-label="Print" href="javascript:void(0);" class="btn-icon white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!----><i ng-if="::$ctrl.btnIcon" am-tooltip="true" class="fa-print fa" am-data-toggle="tooltip" data-html="true" data-placement="left" data-container="false" data-original-title="Print" data-toggle="tooltip"></i><!----></a></am-button><!---->                       </li><!---->                       <!----><li ng-if="$ctrl.isHelpSupported()">                           <!----><am-help ng-if="$ctrl.isHelpSupported()" style=""><am-button btn-type="icon" tooltip-text="Help" btn-icon="fa-question-circle"><a role="button" aria-label="Help" href="javascript:void(0);" class="btn-icon white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!----><i ng-if="::$ctrl.btnIcon" am-tooltip="true" class="fa-question-circle fa" am-data-toggle="tooltip" data-html="true" data-placement="left" data-container="false" data-original-title="Help" data-toggle="tooltip"></i><!----></a></am-button></am-help><!---->                       </li><!---->                    </ul>                </span>            </div>        </div>    </div></section><!----></am-page-header>
</div></am-bar-page-header>
            
            
        
        
        <div id="amContents" class="ibkr-am-contents no-margin">
            <am-page><div class="page-content" id="amPageContent" style="min-height: 506.516px;"><!----><!----><!----><div ng-if="::$ctrl.isAmDependency"><!----><am-page-breadcrumbs><section ng-show="$ctrl.getBreadcrumbs().length > 1" class="breadcrumb-container ng-hide" style="">    <div class="container">        <div class="row">            <div class="col-12 col-sm-12 col-md-12 col-lg-12">                <ol class="breadcrumb">                    <!----><li ng-repeat="bc in $ctrl.getBreadcrumbs()" ng-class="{ 'active' : $last }" class="active">                        <a ng-hide="$last" ng-click="$ctrl.onBreadcrumbClicked($index)" href="javascript:void(0);" class="ng-hide">                            Statements                        </a>                        <span ng-show="$last" class="">                            Statements                        </span>                    </li><!---->                </ol>            </div>        </div>    </div></section></am-page-breadcrumbs></div><!----><div ng-show="!$ctrl.isPageLoading()" ng-class="{ 'wrapper-full' : $ctrl.isFullWrapper(), 'wrapper' : !$ctrl.isFullWrapper() }" class="wrapper"><div class="container"><component-include path="$ctrl.getPageSource()" onload="$ctrl.finishPageContent()" suppress-child-notifications="true"><div><script type="text/javascript" src="/AccountManagement/script/service/reporting/amReportingService.js"></script>
<script type="text/javascript" src="/AccountManagement/script/page/reporting/reports/reportsController.js"></script>
<script type="text/javascript" src="/AccountManagement/script/amReporting.js"></script>

<div class="row" ng-controller="ReportsCtrl as ctrl">
    <div class="col-12 col-sm-12 col-md-12 col-lg-12">
        <!-- TABS START -->
        <ul class="nav nav-tabs nav-lines-large" data-fa-overlay-menu="true">
            <!----><li ng-repeat="tab in ctrl.accessibleTabs" class="nav-item">
                <a href="javascript:void(0);" class="nav-link active" ng-class="{ 'active' : ctrl.activeTab === tab }" ng-click="ctrl.setActiveTab(tab);" data-toggle="tab" data-target="Statements">
                    Statements
                    <!---->
                </a>
            </li><!----><li ng-repeat="tab in ctrl.accessibleTabs" class="nav-item">
                <a href="javascript:void(0);" class="nav-link" ng-class="{ 'active' : ctrl.activeTab === tab }" ng-click="ctrl.setActiveTab(tab);" data-toggle="tab" data-target="FlexQueries">
                    Flex Queries
                    <!---->
                </a>
            </li><!----><li ng-repeat="tab in ctrl.accessibleTabs" class="nav-item">
                <a href="javascript:void(0);" class="nav-link" ng-class="{ 'active' : ctrl.activeTab === tab }" ng-click="ctrl.setActiveTab(tab);" data-toggle="tab" data-target="OtherReports">
                    Other Reports
                    <!---->
                </a>
            </li><!----><li ng-repeat="tab in ctrl.accessibleTabs" class="nav-item">
                <a href="javascript:void(0);" class="nav-link" ng-class="{ 'active' : ctrl.activeTab === tab }" ng-click="ctrl.setActiveTab(tab);" data-toggle="tab" data-target="TaxReports">
                    Tax
                    <!---->
                </a>
            </li><!----><li ng-repeat="tab in ctrl.accessibleTabs" class="nav-item">
                <a href="javascript:void(0);" class="nav-link" ng-class="{ 'active' : ctrl.activeTab === tab }" ng-click="ctrl.setActiveTab(tab);" data-toggle="tab" data-target="ThirdPartyReports">
                    Third-Party Reports
                    <!---->
                </a>
            </li><!---->
        </ul>
        <!-- TABS END -->
        
        <!-- TABS CONTENT START -->
        <div class="tab-content">
            <!-- STATEMENTS START -->
            <!----><div ng-if="ctrl.activeTab === 'Statements'" role="tabpanel" id="Statements" class="tab-pane active" ng-class="{ 'active' : ctrl.activeTab === 'Statements' }">
                <!----><ng-include src="'/AccountManagement/template/page/reporting/statements/statements.html'"><script type="text/javascript" src="/AccountManagement/script/amStatements.js"></script>
<script type="text/javascript" src="/AccountManagement/script/amStatementsModal.js"></script>
<script type="text/javascript" src="/AccountManagement/script/service/reporting/amReportingService.js"></script>
<script type="text/javascript" src="/AccountManagement/script/page/reporting/statementsViewerController.js"></script>

<div class="row" ng-controller="StatementsCtrl as ctrl">
    <div ng-show="ctrl.isFetching()" class="col-12 col-sm-12 col-md-12 col-lg-12 ng-hide">
        <!----><ng-include src="'/AccountManagement/template/page/reporting/common/loading.html'"><section class="panel">
	<div class="panel-body">
		<div class="row">
			<div class="col-12 col-sm-8 offset-sm-2 col-md-8 offset-md-2 col-lg-4 offset-lg-4">
				<br> <br>
				<h2 class="text-center text-dark">Your report is being generated.</h2>
				<br>
				<div class="load-bar">
					<div class="progress">
						<div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100"></div>
					</div>
				</div>
				<br> <br>
			</div>
		</div>
	</div>
</section>
</ng-include>
    </div>
    <!---->
    <div ng-show="!ctrl.isFetching()" class="col-12 col-sm-12 col-md-12 col-lg-8">
        <!-- DEFAULT STATEMENTS START -->
        <!----><section ng-if="ctrl.statementTypes &amp;&amp; ctrl.statementTypes.length > 0" class="panel">
            <div class="panel-heading">
                <span class="heading">Default Statements</span> 
                <span class="btn-group-right">
                    <ul class="btn-icons">
                        <li><am-help sub-id="DefaultStatements" style=""><am-button btn-type="icon" tooltip-text="Help" btn-icon="fa-question-circle"><a role="button" aria-label="Help" href="javascript:void(0);" class="btn-icon white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!----><i ng-if="::$ctrl.btnIcon" am-tooltip="true" class="fa-question-circle fa" am-data-toggle="tooltip" data-html="true" data-placement="left" data-container="false" data-original-title="Help" data-toggle="tooltip"></i><!----></a></am-button></am-help></li>
                    </ul>
                </span>
            </div>
            <div class="panel-body panel-padding">
                <loading-overlay ng-show="ctrl.isFetchingConfig" class="ng-hide"><div class="refresh-block"><span class="refresh-loader"><i class="fa fa-spinner fa-spin fa-2x"></i></span></div></loading-overlay>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                        <!-- ERROR HANDLING START -->
                        <!---->
                        <!-- ERROR HANDLING END -->
                        <div class="form-bordered">
                            <!----><div class="row" ng-repeat="statement in ctrl.statementTypes">
                                <div class="col-2 col-sm-1 col-md-1 col-lg-1">
                                    <p class="text-center">
                                        <a href="javascript:void(0)" class="btn-icon" ng-click="ctrl.openReviewModal(statement.key);">
                                            <i class="fa fa-circle-info" am-tooltip="" data-toggle="tooltip" data-placement="right" data-original-title="Info"></i>
                                        </a>
                                    </p>
                                </div>
                                <div class="col-6 col-sm-8 col-md-8 col-lg-8">
                                    <p>
                                        <strong>Activity</strong>
                                    </p>
                                </div>
                                <div class="col-4 col-sm-3 col-md-3 col-lg-3">
                                    <p>
                                        <span class="btn-group-right"> 
                                            <a href="javascript:void(0);" ng-click="ctrl.openOptionsModal(statement.key, 'DEFAULT_STATEMENT')" class="btn-icon"> 
                                                <i class="fa fa-circle-arrow-right" aria-hidden="true" data-original-title="Run" data-toggle="tooltip" data-placement="left" am-tooltip=""></i>
                                            </a>
                                        </span>
                                    </p>
                                </div>
                            </div><!----><div class="row" ng-repeat="statement in ctrl.statementTypes">
                                <div class="col-2 col-sm-1 col-md-1 col-lg-1">
                                    <p class="text-center">
                                        <a href="javascript:void(0)" class="btn-icon" ng-click="ctrl.openReviewModal(statement.key);">
                                            <i class="fa fa-circle-info" am-tooltip="" data-toggle="tooltip" data-placement="right" data-original-title="Info"></i>
                                        </a>
                                    </p>
                                </div>
                                <div class="col-6 col-sm-8 col-md-8 col-lg-8">
                                    <p>
                                        <strong>MTM Summary</strong>
                                    </p>
                                </div>
                                <div class="col-4 col-sm-3 col-md-3 col-lg-3">
                                    <p>
                                        <span class="btn-group-right"> 
                                            <a href="javascript:void(0);" ng-click="ctrl.openOptionsModal(statement.key, 'DEFAULT_STATEMENT')" class="btn-icon"> 
                                                <i class="fa fa-circle-arrow-right" aria-hidden="true" data-original-title="Run" data-toggle="tooltip" data-placement="left" am-tooltip=""></i>
                                            </a>
                                        </span>
                                    </p>
                                </div>
                            </div><!----><div class="row" ng-repeat="statement in ctrl.statementTypes">
                                <div class="col-2 col-sm-1 col-md-1 col-lg-1">
                                    <p class="text-center">
                                        <a href="javascript:void(0)" class="btn-icon" ng-click="ctrl.openReviewModal(statement.key);">
                                            <i class="fa fa-circle-info" am-tooltip="" data-toggle="tooltip" data-placement="right" data-original-title="Info"></i>
                                        </a>
                                    </p>
                                </div>
                                <div class="col-6 col-sm-8 col-md-8 col-lg-8">
                                    <p>
                                        <strong>Realized Summary</strong>
                                    </p>
                                </div>
                                <div class="col-4 col-sm-3 col-md-3 col-lg-3">
                                    <p>
                                        <span class="btn-group-right"> 
                                            <a href="javascript:void(0);" ng-click="ctrl.openOptionsModal(statement.key, 'DEFAULT_STATEMENT')" class="btn-icon"> 
                                                <i class="fa fa-circle-arrow-right" aria-hidden="true" data-original-title="Run" data-toggle="tooltip" data-placement="left" am-tooltip=""></i>
                                            </a>
                                        </span>
                                    </p>
                                </div>
                            </div><!----><div class="row" ng-repeat="statement in ctrl.statementTypes">
                                <div class="col-2 col-sm-1 col-md-1 col-lg-1">
                                    <p class="text-center">
                                        <a href="javascript:void(0)" class="btn-icon" ng-click="ctrl.openReviewModal(statement.key);">
                                            <i class="fa fa-circle-info" am-tooltip="" data-toggle="tooltip" data-placement="right" data-original-title="Info"></i>
                                        </a>
                                    </p>
                                </div>
                                <div class="col-6 col-sm-8 col-md-8 col-lg-8">
                                    <p>
                                        <strong>Commodities Regulatory</strong>
                                    </p>
                                </div>
                                <div class="col-4 col-sm-3 col-md-3 col-lg-3">
                                    <p>
                                        <span class="btn-group-right"> 
                                            <a href="javascript:void(0);" ng-click="ctrl.openOptionsModal(statement.key, 'DEFAULT_STATEMENT')" class="btn-icon"> 
                                                <i class="fa fa-circle-arrow-right" aria-hidden="true" data-original-title="Run" data-toggle="tooltip" data-placement="left" am-tooltip=""></i>
                                            </a>
                                        </span>
                                    </p>
                                </div>
                            </div><!----><div class="row" ng-repeat="statement in ctrl.statementTypes">
                                <div class="col-2 col-sm-1 col-md-1 col-lg-1">
                                    <p class="text-center">
                                        <a href="javascript:void(0)" class="btn-icon" ng-click="ctrl.openReviewModal(statement.key);">
                                            <i class="fa fa-circle-info" am-tooltip="" data-toggle="tooltip" data-placement="right" data-original-title="Info"></i>
                                        </a>
                                    </p>
                                </div>
                                <div class="col-6 col-sm-8 col-md-8 col-lg-8">
                                    <p>
                                        <strong>Trade Confirmation</strong>
                                    </p>
                                </div>
                                <div class="col-4 col-sm-3 col-md-3 col-lg-3">
                                    <p>
                                        <span class="btn-group-right"> 
                                            <a href="javascript:void(0);" ng-click="ctrl.openOptionsModal(statement.key, 'DEFAULT_STATEMENT')" class="btn-icon"> 
                                                <i class="fa fa-circle-arrow-right" aria-hidden="true" data-original-title="Run" data-toggle="tooltip" data-placement="left" am-tooltip=""></i>
                                            </a>
                                        </span>
                                    </p>
                                </div>
                            </div><!----><div class="row" ng-repeat="statement in ctrl.statementTypes">
                                <div class="col-2 col-sm-1 col-md-1 col-lg-1">
                                    <p class="text-center">
                                        <a href="javascript:void(0)" class="btn-icon" ng-click="ctrl.openReviewModal(statement.key);">
                                            <i class="fa fa-circle-info" am-tooltip="" data-toggle="tooltip" data-placement="right" data-original-title="Info"></i>
                                        </a>
                                    </p>
                                </div>
                                <div class="col-6 col-sm-8 col-md-8 col-lg-8">
                                    <p>
                                        <strong>Aggregated Costs and Charges</strong>
                                    </p>
                                </div>
                                <div class="col-4 col-sm-3 col-md-3 col-lg-3">
                                    <p>
                                        <span class="btn-group-right"> 
                                            <a href="javascript:void(0);" ng-click="ctrl.openOptionsModal(statement.key, 'DEFAULT_STATEMENT')" class="btn-icon"> 
                                                <i class="fa fa-circle-arrow-right" aria-hidden="true" data-original-title="Run" data-toggle="tooltip" data-placement="left" am-tooltip=""></i>
                                            </a>
                                        </span>
                                    </p>
                                </div>
                            </div><!---->
                        </div>
                    </div>
                </div>
            </div>
        </section><!---->
        <!-- DEFAULT STATEMENTS END -->
        
        <!-- CUSTOM STATEMENTS START -->
        <!----><am-item-manipulator ng-if="ctrl.isCustomStmtsAccessible" item-template="custom-statements-data.component" manipulator-id="ActivityStatements"><component-include path="$itemManipulator.itemTemplate" onload="$itemManipulator.onload()"><div><custom-statements-data> <section class="panel">
    <div class="panel-heading">
        <span class="heading">Custom Statements</span> 
        <span class="btn-group-right">
            <ul class="btn-icons">
                <li>
                    <a href="javascript:void(0);" ng-click="$ctrl.navigateToCreateEdit()" class="btn-icon"><i class="fa fa-plus tooltips" data-toggle="tooltip" data-placement="left" data-original-title="Create" am-tooltip=""></i></a>
                </li>
                <li><am-help sub-id="CustomStatements" style=""><am-button btn-type="icon" tooltip-text="Help" btn-icon="fa-question-circle"><a role="button" aria-label="Help" href="javascript:void(0);" class="btn-icon white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!----><i ng-if="::$ctrl.btnIcon" am-tooltip="true" class="fa-question-circle fa" am-data-toggle="tooltip" data-html="true" data-placement="left" data-container="false" data-original-title="Help" data-toggle="tooltip"></i><!----></a></am-button></am-help></li>
            </ul>
        </span>
    </div>
    <div class="panel-body panel-padding">
        <loading-overlay ng-show="$ctrl.isDownloading" class="ng-hide"><div class="refresh-block"><span class="refresh-loader"><i class="fa fa-spinner fa-spin fa-2x"></i></span></div></loading-overlay>
        <!---->
        <div class="row">
            <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                <!---->
                <!-- ERROR HANDLING START -->
                <!---->
                <!-- ERROR HANDLING END -->
                <!-- IF NO CUSTOM STATEMENT HAS BEEN CREATED YET -->
                <!----><div ng-if="!$ctrl.filtered &amp;&amp; $ctrl.$itemManipulator.entries.length === 0" class="alert-no-results">
                    <p ng-bind-html="'statements.info.noStmtsMsg' | translate | renderHTML">You do not have any custom statements. To create your own custom statement, click on the <i class="fa fa-plus"></i> in the top right corner of the panel.</p>
                </div><!---->
                <!-- IF NO RESULTS FOUND. -->
                <!---->
                <!---->
            </div>
        </div>
        <!---->
    </div>
</section></custom-statements-data></div></component-include></am-item-manipulator><!---->
        <!-- CUSTOM STATEMENTS END -->
    </div>
    <div ng-show="!ctrl.isFetching()" class="col-12 col-sm-12 col-md-12 col-lg-4">
        <!-- BANNER ADS START -->
        <reporting-adviewer><div><div class="infoViewer" style="max-height: 400px; max-width: 1280px;"><img src="blob:https://www.interactivebrokers.co.uk/430b5980-c81e-4d70-aec8-a5c754b3a2be" alt="250-0-1280x400-en.jpg" style="max-height: 400px; max-width: 1280px;"></div></div><br ng-hide="$ctrl.hideBreak" class="ng-hide"></reporting-adviewer>
        <!-- BANNER ADS END -->
    
        <!-- BATCH REPORTS START -->
        <!---->
        <!-- BATCH REPORTS END -->

        <!-- DELIVERED STATEMENTS START -->
        <!----><section class="panel" ng-if="ctrl.isDeliveredStmtsAccessible" ng-hide="ctrl.isMobileTws()">
            <div class="panel-heading">
                <span class="heading">Statements Delivery</span>
                <span class="btn-group-right"> <a href="" class="btn-icon" ng-click="ctrl.navigateToDeliveryConfig()"><i class="fa fa-gear tooltips" data-toggle="tooltip" data-placement="left" data-original-title="Configure" am-tooltip=""></i>
                </a> <am-help sub-id="DeliveredStatements" style=""><am-button btn-type="icon" tooltip-text="Help" btn-icon="fa-question-circle"><a role="button" aria-label="Help" href="javascript:void(0);" class="btn-icon white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!----><i ng-if="::$ctrl.btnIcon" am-tooltip="true" class="fa-question-circle fa" am-data-toggle="tooltip" data-html="true" data-placement="left" data-container="false" data-original-title="Help" data-toggle="tooltip"></i><!----></a></am-button></am-help>
                </span>
            </div>
            <div class="panel-body" ng-class="{'panel-padding': !ctrl.employeeTrackInstruction &amp;&amp; !ctrl.defaultInstructions.length &amp;&amp; !ctrl.isCustomDeliveryConfigured}">
                <loading-overlay ng-show="ctrl.fetchingDeliveryData" class="ng-hide"><div class="refresh-block"><span class="refresh-loader"><i class="fa fa-spinner fa-spin fa-2x"></i></span></div></loading-overlay>
                <div class="row">
                    <div class="col">
                        <!-- IF NO DELIVERED STATEMENT HAS BEEN CREATED YET -->
                        <!---->
                        <!---->
                        <!----><div class="link-set" ng-if="ctrl.defaultInstructions.length > 0">
                            <!----><div class="link-row" ng-repeat="instrcution in ctrl.defaultInstructions">
                                <div class="icon-before">
                                    <i ng-class="{'fa fa-paperclip': instrcution.deliveryMode === 'E', 'far fa-comment': instrcution.deliveryMode === 'A', 'far fa-envelope': instrcution.deliveryMode === 'N'}" class="tooltips far fa-envelope" aria-hidden="true" data-original-title="Email" data-toggle="tooltip" data-placement="right" am-tooltip=""></i>
                                </div>
                                <div class="link-text">
                                    <p>
                                        <span class="link-label">
                                            Daily Activity Statement
                                        </span>
                                    </p>
                                </div>
                            </div><!----><div class="link-row" ng-repeat="instrcution in ctrl.defaultInstructions">
                                <div class="icon-before">
                                    <i ng-class="{'fa fa-paperclip': instrcution.deliveryMode === 'E', 'far fa-comment': instrcution.deliveryMode === 'A', 'far fa-envelope': instrcution.deliveryMode === 'N'}" class="tooltips fa fa-paperclip" aria-hidden="true" data-original-title="Email with Attachment" data-toggle="tooltip" data-placement="right" am-tooltip=""></i>
                                </div>
                                <div class="link-text">
                                    <p>
                                        <span class="link-label">
                                            Monthly Activity Statement
                                        </span>
                                    </p>
                                </div>
                            </div><!----><div class="link-row" ng-repeat="instrcution in ctrl.defaultInstructions">
                                <div class="icon-before">
                                    <i ng-class="{'fa fa-paperclip': instrcution.deliveryMode === 'E', 'far fa-comment': instrcution.deliveryMode === 'A', 'far fa-envelope': instrcution.deliveryMode === 'N'}" class="tooltips fa fa-paperclip" aria-hidden="true" data-original-title="Email with Attachment" data-toggle="tooltip" data-placement="right" am-tooltip=""></i>
                                </div>
                                <div class="link-text">
                                    <p>
                                        <span class="link-label">
                                            Daily Trade Report
                                        </span>
                                    </p>
                                </div>
                            </div><!---->
                        </div><!---->
                        <!---->
                    </div>
                </div>
            </div>
        </section><!---->
        <!-- DELIVERED STATEMENTS END -->
        
        <!-- FULL SERVICE CLIENT DEFAULT STATEMENTS DELIVERY START -->
        <!---->
        <!--  FULL SERVICE CLIENT DEFAULT STATEMENTS DELIVERY END -->
        
    </div>
</div></ng-include>
            </div><!---->
            <!-- STATEMENTS END -->
            
            <!-- FLEX QUERIES START -->
            <!---->
            <!-- FLEX QUERIES END -->
            
            <!-- ADVISOR REPORTS START -->
            <!---->
            <!-- ADVISOR REPORTS END -->
            
            <!-- OTHER REPORTS START -->
            <!---->
            <!-- OTHER REPORTS END -->
            
            <!-- OTHER REPORTS (reporting-war) START -->
            <!---->
            <!-- OTHER REPORTS (reporting-war) END -->
            
            <!-- TAX REPORTS START -->
            <!---->
            <!-- TAX REPORTS END -->
            
            <!-- THIRD PARTY REPORTS START -->
            <!---->
            <!-- THIRD PARTY REPORTS END -->
            
            <!-- TRANSACTION HISTORY START -->
            <!---->
            <!-- TRANSACTION HISTORY END -->
            
            <!-- INTERNAL TOOLS START -->
            <!---->
            
        </div>
        <!-- TABS CONTENT END -->
    </div>
</div></div></component-include></div></div></div></am-page>
        </div>
        
        <am-footer><!----></am-footer>
        
        <am-picker><div id="amPicker"><am-item-manipulator item-template="picker-account-data.component" manipulator-id="Picker" onload="$ctrl.pickerInitialize()"><component-include path="$itemManipulator.itemTemplate" onload="$itemManipulator.onload()"><div><picker-account-data><div class="sb-slidebar sb-right sb-style-overlay noprint" style="right: -400px;">
    <div class="account-panel">
        <div class="row">
            <div class="col">
                <div class="account-panel-header">
                    <div class="row align-items-center">
                        <div class="col-10 col-sm-9">
                            <h5>Select Account(s)</h5>
                        </div>
                        <div class="col-2 col-sm-3">
                            <div ng-show="$ctrl.isPickerClosable()" class="btn-close">
                                <a href="javascript:void(0);" target="_self" class="btn-icon sb-close">
                                    <i class="fas fa-times fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="account-panel-filters">
                    <div class="row align-items-center">
                        <div class="col-8 col-sm-9">
                            <freeform-text-filter filter-id="pickerText" placeholder-key-prefix="data.filter.option.placeholder."><!----></freeform-text-filter>
                        </div>
                        <div class="col-4 col-sm-3">
                            <div class="btn-group-right">
                                <ul class="btn-icons">
                                    <dropdown-filters filter-key-prefix="data.filter.name." option-key-prefix-map="$ctrl.filterOptionMapping"><!----><li ng-if="$ctrl.dropdownFilters.length > 0" persistent-dropdown-parent="" class="persistent-dropdown">
    <am-button btn-type="icon" btn-icon="fa-filter" tooltip-text="Filter" btn-class="active"><a role="button" aria-label="Filter" href="javascript:void(0);" class="btn-icon white-space-normal active" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!----><i ng-if="::$ctrl.btnIcon" am-tooltip="true" class="fa-filter fa" am-data-toggle="tooltip" data-html="true" data-placement="left" data-container="false" data-original-title="Filter" data-toggle="tooltip"></i><!----></a></am-button>
    
    <div class="dropdown-menu dropdown-content dropdown-right">
        <p class="title">Filter Results</p>
        
        <div ng-show="$ctrl.dropdownFilters.length === 1" class="btn-selectors">
            <!----><p ng-repeat="filterOption in $ctrl.dropdownFilters[0].options">
                <a ng-click="$ctrl.$itemManipulator.toggleFilter(filterOption, false)" href="javascript:void(0);" class="btn btn-default active" ng-class="{ 'active' : filterOption.enabled }">
                    Migrated
                </a>
            </p><!----><p ng-repeat="filterOption in $ctrl.dropdownFilters[0].options">
                <a ng-click="$ctrl.$itemManipulator.toggleFilter(filterOption, false)" href="javascript:void(0);" class="btn btn-default active" ng-class="{ 'active' : filterOption.enabled }">
                    Open
                </a>
            </p><!---->
        </div>
        
        <div ng-show="$ctrl.dropdownFilters.length > 1" class="ng-hide">
            <div class="accordion accordion-filter" id="Picker_accordion" role="tablist" aria-multiselectable="true">
                <!----><div class="accordion-item" ng-repeat="filter in $ctrl.dropdownFilters">
                    <h2 class="accordion-header active" ng-class="{ 'active' : filter.enabled, 'disabled' : (filter.options.length <= 1 &amp;&amp; filter.type === 'MULTIPLE') }" role="tab">
                        <a class="accordion-button collapsed filter-active" href="javascript:void(0);" role="button" am-data-toggle="collapse" am-data-parent="#Picker_accordion" am-data-target="#accountStatus" ng-class="{ 'filter-active' : filter.enabled }" aria-controls="accountStatus" data-toggle="collapse" data-target="#accountStatus" data-parent="#Picker_accordion">
                            &nbsp;Account Status
                        </a>
                    </h2>
                    <!----><div ng-if="filter.type !== 'RANGE'" id="accountStatus" class="accordion-collapse collapse" role="tabpanel">
                        <div class="accordion-body">
                             <div class="table-responsive">
                                 <table width="100%" cellpadding="0" cellspacing="0" border="0" class="table table-hover">
                                     <!----><tbody ng-if="$ctrl.filterGrouping[filter.id] === undefined">
                                         <!----><tr ng-click="$ctrl.$itemManipulator.toggleFilter(option, false)" ng-repeat="option in filter.options" ng-class="{ 'active' : option.enabled }" class="active">
                                             <td width="100%" align="left" valign="middle">
                                                 Migrated
                                             </td>
                                         </tr><!----><tr ng-click="$ctrl.$itemManipulator.toggleFilter(option, false)" ng-repeat="option in filter.options" ng-class="{ 'active' : option.enabled }" class="active">
                                             <td width="100%" align="left" valign="middle">
                                                 Open
                                             </td>
                                         </tr><!---->
                                     </tbody><!---->
                                     <!---->
                                 </table>
                             </div>
                         </div>
                    </div><!---->
                    <!---->
                </div><!---->
            </div>
        </div>
        
        <div class="btn-applicators">
            <div class="row">
                <div class="col-6">
                    <p>
                        <am-button on-click="$ctrl.resetFilters()" btn-text="Reset" persistent-dropdown-closer="" class="persistent-dropdown-closer"><a role="button" href="javascript:void(0);" class="btn btn-default white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!---->Reset</a></am-button>
                    </p>
                </div>
                <div class="col-6">
                    <p>
                        <am-button on-click="$ctrl.$itemManipulator.updateFilters()" btn-type="info" btn-text="Apply" persistent-dropdown-closer="" class="persistent-dropdown-closer"><a role="button" href="javascript:void(0);" class="btn btn-info white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!---->Apply</a></am-button>
                    </p>
                </div>
            </div>
        </div>
    </div>
</li><!----></dropdown-filters>
                                    <table-preference-config preference="$ctrl.getTablePreference()" preference-name="pickerColumns" preference-key-prefix="picker.preference.columns."><!----><li ng-if="$ctrl.hasTablePreference()" persistent-dropdown-parent="" class="persistent-dropdown">   <am-button btn-type="icon" btn-icon="fa-cog" tooltip-text="Configure"><a role="button" aria-label="Configure" href="javascript:void(0);" class="btn-icon white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!----><i ng-if="::$ctrl.btnIcon" am-tooltip="true" class="fa-cog fa" am-data-toggle="tooltip" data-html="true" data-placement="left" data-container="false" data-original-title="Configure" data-toggle="tooltip"></i><!----></a></am-button>   <div class="dropdown-menu dropdown-content dropdown-right">       <p class="title">Columns</p>       <div class="btn-selectors">           <!----><p ng-repeat="column in $ctrl.preference">               <!---->           </p><!----><p ng-repeat="column in $ctrl.preference">               <!----><a ng-if="!column.forced" ng-click="!column.disabled &amp;&amp; $ctrl.toggleColumn(column)" href="javascript:void(0);" class="btn btn-default active" ng-class="{ 'active' : column.displayEnabled, 'disabled' : column.disabled }">                   Alias               </a><!---->           </p><!----><p ng-repeat="column in $ctrl.preference">               <!----><a ng-if="!column.forced" ng-click="!column.disabled &amp;&amp; $ctrl.toggleColumn(column)" href="javascript:void(0);" class="btn btn-default active" ng-class="{ 'active' : column.displayEnabled, 'disabled' : column.disabled }">                   Customer Type               </a><!---->           </p><!----><p ng-repeat="column in $ctrl.preference">               <!----><a ng-if="!column.forced" ng-click="!column.disabled &amp;&amp; $ctrl.toggleColumn(column)" href="javascript:void(0);" class="btn btn-default" ng-class="{ 'active' : column.displayEnabled, 'disabled' : column.disabled }">                   Account Title               </a><!---->           </p><!----><p ng-repeat="column in $ctrl.preference">               <!----><a ng-if="!column.forced" ng-click="!column.disabled &amp;&amp; $ctrl.toggleColumn(column)" href="javascript:void(0);" class="btn btn-default" ng-class="{ 'active' : column.displayEnabled, 'disabled' : column.disabled }">                   Status               </a><!---->           </p><!---->       </div>       <div class="btn-applicators">           <p>               <am-button on-click="$ctrl.applyColumns()" btn-type="info" btn-text="Apply" persistent-dropdown-closer="" class="persistent-dropdown-closer"><a role="button" href="javascript:void(0);" class="btn btn-info white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!---->Apply</a></am-button>           </p>       </div>   </div></li><!----></table-preference-config>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="account-panel-content">
                    <div class="account-panel-table">
                    
                        <!---->
                        <!---->
                    
                        <div class="table-responsive">
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" class="table table-hover tablesorter" id="tableAccounts">
                                <thead>
                                    <tr>
                                        <th width="" align="center" valign="middle" class="sorter-false">&nbsp;</th>
                                        <th width="" align="center" valign="middle" class="sorter-false">
                                            <!----><a ng-if="$ctrl.selectAllAllowed" ng-click="$ctrl.toggleSelectAll()" href="javascript:void(0)">
                                                <i class="far fa-lg fa-square" ng-class="{ 'fa-check-square' : $ctrl.allSelected, 'fa-square' : !$ctrl.allSelected }"></i>
                                            </a><!---->
                                        </th>
                                        <!----><!----><th ng-if="$ctrl.isColumnEnabled(column)" ng-click="$ctrl.toggleSort(column)" align="left" valign="middle" class="header" ng-repeat="column in $ctrl.$itemManipulator.manipulator.preferences.pickerColumnOrdering">
                                            Account
                                        </th><!----><!----><!----><th ng-if="$ctrl.isColumnEnabled(column)" ng-click="$ctrl.toggleSort(column)" align="left" valign="middle" class="header" ng-repeat="column in $ctrl.$itemManipulator.manipulator.preferences.pickerColumnOrdering">
                                            Alias
                                        </th><!----><!----><!----><th ng-if="$ctrl.isColumnEnabled(column)" ng-click="$ctrl.toggleSort(column)" align="left" valign="middle" class="header" ng-repeat="column in $ctrl.$itemManipulator.manipulator.preferences.pickerColumnOrdering">
                                            Customer Type
                                        </th><!----><!----><!----><!----><!----><!---->
                                    </tr>
                                </thead>
                                
                                <!----><tbody ng-repeat="pickerEntry in $ctrl.$itemManipulator.entries">
                                    <tr ng-class="{ 'active' : $ctrl.isEntrySelected(pickerEntry), 'disabled' : $ctrl.isEntryDisabled(pickerEntry) }" class="active">                            
                                        <td ng-click="$ctrl.specialAction(pickerEntry)" align="center" valign="middle">
                                            <picker-action-icon picker-entry="pickerEntry"><i class="fa fa-lg float-right text-gray margin-top-5" ng-class="{ 'fa-chevron-right' : pickerEntry.subEntries.length > 0 &amp;&amp; !pickerEntry.showSubEntries,  'fa-chevron-down' : pickerEntry.subEntries.length > 0 &amp;&amp; pickerEntry.showSubEntries,  'fa-sitemap' : pickerEntry.isMultiTiered  }"></i></picker-action-icon>
                                        </td>
                                        
                                        <td ng-click="$ctrl.entryIconClicked(pickerEntry)" align="center" valign="middle">
                                            <picker-entry-icon picker-entry="pickerEntry"><!----><i ng-if="!$ctrl.isEntryDisabled(pickerEntry) &amp;&amp; !pickerEntry.isTierAccessEntry" class="fa-lg fa fa-check" ng-class="{ 'far fa-circle' : !$ctrl.isEntryPartiallySelected(pickerEntry) &amp;&amp; !$ctrl.isEntrySelected(pickerEntry) &amp;&amp; !$ctrl.pickerRules.isMultiSelect,  'far fa-check-circle' : $ctrl.isEntrySelected(pickerEntry) &amp;&amp; !$ctrl.pickerRules.isMultiSelect,  'fa fa-circle' : $ctrl.isEntryPartiallySelected(pickerEntry) &amp;&amp; !$ctrl.pickerRules.isMultiSelect,  'far fa-square' : !$ctrl.isEntryPartiallySelected(pickerEntry) &amp;&amp; !$ctrl.isEntrySelected(pickerEntry) &amp;&amp; $ctrl.pickerRules.isMultiSelect,  'fa fa-check' : $ctrl.isEntrySelected(pickerEntry) &amp;&amp; $ctrl.pickerRules.isMultiSelect,  'far fa-minus-square' : $ctrl.isEntryPartiallySelected(pickerEntry) &amp;&amp; $ctrl.pickerRules.isMultiSelect  }"></i><!----></picker-entry-icon>
                                        </td>
                                        
                                        <!----><!----><td ng-click="$ctrl.entryClicked(pickerEntry)" ng-if="$ctrl.isColumnEnabled(columnName)" align="left" valign="middle" ng-repeat="columnName in $ctrl.getColumnOrderingPreference()">
                                            <ng-switch on="pickerEntry.pickerCustomerType">
                                                <!---->
                                                <!----><any ng-switch-default="">
                                                    <ng-switch on="columnName">
                                                        <!----><span ng-switch-when="accountVan">*********</span><!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                    </ng-switch>
                                                </any><!---->
                                            </ng-switch>
                                        </td><!----><!----><!----><td ng-click="$ctrl.entryClicked(pickerEntry)" ng-if="$ctrl.isColumnEnabled(columnName)" align="left" valign="middle" ng-repeat="columnName in $ctrl.getColumnOrderingPreference()">
                                            <ng-switch on="pickerEntry.pickerCustomerType">
                                                <!---->
                                                <!----><any ng-switch-default="">
                                                    <ng-switch on="columnName">
                                                        <!---->
                                                        <!---->
                                                        <!----><span ng-switch-when="accountAlias"></span><!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                    </ng-switch>
                                                </any><!---->
                                            </ng-switch>
                                        </td><!----><!----><!----><td ng-click="$ctrl.entryClicked(pickerEntry)" ng-if="$ctrl.isColumnEnabled(columnName)" align="left" valign="middle" ng-repeat="columnName in $ctrl.getColumnOrderingPreference()">
                                            <ng-switch on="pickerEntry.pickerCustomerType">
                                                <!---->
                                                <!----><any ng-switch-default="">
                                                    <ng-switch on="columnName">
                                                        <!---->
                                                        <!----><span ng-switch-when="customerType">Individual</span><!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                    </ng-switch>
                                                </any><!---->
                                            </ng-switch>
                                        </td><!----><!----><!----><!----><!----><!---->
                                    </tr>
                                    <!---->
                                </tbody><!----><tbody ng-repeat="pickerEntry in $ctrl.$itemManipulator.entries">
                                    <tr ng-class="{ 'active' : $ctrl.isEntrySelected(pickerEntry), 'disabled' : $ctrl.isEntryDisabled(pickerEntry) }">                            
                                        <td ng-click="$ctrl.specialAction(pickerEntry)" align="center" valign="middle">
                                            <picker-action-icon picker-entry="pickerEntry"><i class="fa fa-lg float-right text-gray margin-top-5" ng-class="{ 'fa-chevron-right' : pickerEntry.subEntries.length > 0 &amp;&amp; !pickerEntry.showSubEntries,  'fa-chevron-down' : pickerEntry.subEntries.length > 0 &amp;&amp; pickerEntry.showSubEntries,  'fa-sitemap' : pickerEntry.isMultiTiered  }"></i></picker-action-icon>
                                        </td>
                                        
                                        <td ng-click="$ctrl.entryIconClicked(pickerEntry)" align="center" valign="middle">
                                            <picker-entry-icon picker-entry="pickerEntry"><!----><i ng-if="!$ctrl.isEntryDisabled(pickerEntry) &amp;&amp; !pickerEntry.isTierAccessEntry" class="fa-lg far fa-square" ng-class="{ 'far fa-circle' : !$ctrl.isEntryPartiallySelected(pickerEntry) &amp;&amp; !$ctrl.isEntrySelected(pickerEntry) &amp;&amp; !$ctrl.pickerRules.isMultiSelect,  'far fa-check-circle' : $ctrl.isEntrySelected(pickerEntry) &amp;&amp; !$ctrl.pickerRules.isMultiSelect,  'fa fa-circle' : $ctrl.isEntryPartiallySelected(pickerEntry) &amp;&amp; !$ctrl.pickerRules.isMultiSelect,  'far fa-square' : !$ctrl.isEntryPartiallySelected(pickerEntry) &amp;&amp; !$ctrl.isEntrySelected(pickerEntry) &amp;&amp; $ctrl.pickerRules.isMultiSelect,  'fa fa-check' : $ctrl.isEntrySelected(pickerEntry) &amp;&amp; $ctrl.pickerRules.isMultiSelect,  'far fa-minus-square' : $ctrl.isEntryPartiallySelected(pickerEntry) &amp;&amp; $ctrl.pickerRules.isMultiSelect  }"></i><!----></picker-entry-icon>
                                        </td>
                                        
                                        <!----><!----><td ng-click="$ctrl.entryClicked(pickerEntry)" ng-if="$ctrl.isColumnEnabled(columnName)" align="left" valign="middle" ng-repeat="columnName in $ctrl.getColumnOrderingPreference()">
                                            <ng-switch on="pickerEntry.pickerCustomerType">
                                                <!---->
                                                <!----><any ng-switch-default="">
                                                    <ng-switch on="columnName">
                                                        <!----><span ng-switch-when="accountVan">********</span><!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                    </ng-switch>
                                                </any><!---->
                                            </ng-switch>
                                        </td><!----><!----><!----><td ng-click="$ctrl.entryClicked(pickerEntry)" ng-if="$ctrl.isColumnEnabled(columnName)" align="left" valign="middle" ng-repeat="columnName in $ctrl.getColumnOrderingPreference()">
                                            <ng-switch on="pickerEntry.pickerCustomerType">
                                                <!---->
                                                <!----><any ng-switch-default="">
                                                    <ng-switch on="columnName">
                                                        <!---->
                                                        <!---->
                                                        <!----><span ng-switch-when="accountAlias"></span><!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                    </ng-switch>
                                                </any><!---->
                                            </ng-switch>
                                        </td><!----><!----><!----><td ng-click="$ctrl.entryClicked(pickerEntry)" ng-if="$ctrl.isColumnEnabled(columnName)" align="left" valign="middle" ng-repeat="columnName in $ctrl.getColumnOrderingPreference()">
                                            <ng-switch on="pickerEntry.pickerCustomerType">
                                                <!---->
                                                <!----><any ng-switch-default="">
                                                    <ng-switch on="columnName">
                                                        <!---->
                                                        <!----><span ng-switch-when="customerType">Individual</span><!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                        <!---->
                                                    </ng-switch>
                                                </any><!---->
                                            </ng-switch>
                                        </td><!----><!----><!----><!----><!----><!---->
                                    </tr>
                                    <!---->
                                </tbody><!---->
                            </table>
                        </div>
                    </div>
                    
                    <pagination-controls spacing="false"><!----><!----></pagination-controls>
                    
                    <!---->
                    
                    <!----><br ng-if="!$ctrl.showSelectedAccounts()"><!---->
                    <!---->
                </div>
            </div>
        </div>
    </div>
    
    <div class="panel-btns">
        <div class="row">
            <div class="col-12 col-sm-8 order-2 order-sm-1">
                <div class="panel-btn-left">
                    <p>
                        <!---->
                        <am-button on-click="$ctrl.pickerReset()" btn-text="Reset"><a role="button" href="javascript:void(0);" class="btn btn-default white-space-normal" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!---->Reset</a></am-button>
                    </p>
                </div>
            </div>
            <div class="col-12 col-sm-4 order-1 order-sm-2">
                <div class="panel-btn-right">
                    <p>
                        <am-button on-click="$ctrl.pickerSubmit()" btn-type="primary" btn-text="Continue" btn-class="btn-continue"><a role="button" href="javascript:void(0);" class="btn btn-primary white-space-normal btn-continue" ng-click="!$ctrl.isDisabled() &amp;&amp; $ctrl.onClick()" ng-class="{ 'disabled' : $ctrl.isDisabled() }"><!---->Continue</a></am-button>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!---->
</div></picker-account-data></div></component-include></am-item-manipulator></div></am-picker>
        <am-modal><div id="amModal" class="modal fade" ng-class="::{ 'fade' : $ctrl.isFadeSupported }" tabindex="-1" role="dialog" style="display: hidden;">   <div id="amModalDialog" role="document">       <div id="amModalContent">           <div id="amModalHeader" class="modal-header">               <button type="button" class="close" data-dismiss="modal" data-bs-dismiss="modal" aria-label="Close">                   <span aria-hidden="true">×</span>               </button>               <h4 class="modal-title" aria-level="2">                   <i id="amModalIcon" aria-hidden="true"></i>                   <span id="amModalTitle"></span>               </h4>           </div>           <div id="amModalBody" class="modal-body"></div>           <div id="amModalFooter" class="modal-footer"></div>       </div>   </div></div></am-modal>
        
        
            <div id="cp-footer"></div>
            
            
            	
            	
					<script type="text/javascript" src="/order-ticket/umd1/index.js?_=**********"></script>            	
            	
            
            <script type="text/javascript" src="/lib/onebar/index.js?_=**********"></script>
            <script type="text/javascript">onebar.mount('#onebar', getOneBarOptions());</script>
        
    
<div role="dialog" aria-modal="true" aria-hidden="true" dir="ltr" class="_modal _con bulletin-modal" style="display: none;"><div class="_formo _mdlg" style="width: 800px; height: 30em;"><div class="_mdef"><div class="ib-col flex bulletin-container"><div class="ib-row"><div class="ib-col flex-fixed inset-16 border-bottom"><div><table><tr><td class="bulletin-icon"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 24 24" enable-background="new 0 0 24 24" xml:space="preserve"><path d="M21.4,10.6l-8-8c-0.8-0.8-2-0.8-2.8,0l-8,8c-0.8,0.8-0.8,2,0,2.8l8,8c0.8,0.8,2,0.8,2.8,0l8-8
	C22.2,12.6,22.2,11.4,21.4,10.6z M13,17h-2v-2h2V17z M13,13h-2V7h2V13z"></path></svg></td><td><big class="text-bold">Bulletin</big></td></tr><tr style="display: none;"><td></td><td><small class="fg70">Bulletin 1 of 0</small></td></tr></table></div><div class="arrows" style="display: none;"><a aria-label="Go to next" class="cursor disabled"><svg viewBox="0 0 24 24" role="presentation" focusable="false"><polyline fill="none" stroke-width="2" points="21,8.5 12,17.5 3,8.5 "></polyline></svg></a><a aria-label="Go to previous" class="cursor disabled"><svg viewBox="0 0 50 50" role="presentation" focusable="false"><polygon points="2.75,35 4.836,37.086 25,16.922 45.164,37.086 47.25,35 25,12.75 "></polygon></svg></a></div></div></div><div class="ib-row border-bottom grow"><div class="ib-col inset-16"><p class="bulletin-modal__content"></p></div></div><div class="ib-row"><div class="ib-col inset-16 text-right"><button type="button" class="_btn primary">Next<!----></button></div></div></div></div><!----></div></div></body></html>