/**
 * Utility functions for the Interactive Brokers statement downloader
 */

const fs = require('fs');
const path = require('path');
const { downloadsDir } = require('./config');

/**
 * Creates a logger that writes to both console and a log file
 * @returns {Object} Logger object with log method
 */
function createLogger() {
  const logFilePath = path.join(downloadsDir, `download_log_${new Date().toISOString().replace(/:/g, '-')}.txt`);

  return {
    log: function(message) {
      console.log(message);
      fs.appendFileSync(logFilePath, message + '\n');
    },

    error: function(message) {
      console.error(message);
      fs.appendFileSync(logFilePath, `ERROR: ${message}\n`);
    },

    logFailedDownload: function(date) {
      fs.appendFileSync(path.join(downloadsDir, 'failed_downloads.txt'), `${date}\n`);
    },

    logNoStatementAvailable: function(date) {
      fs.appendFileSync(path.join(downloadsDir, 'no_statements_available.txt'), `${date}\n`);
    }
  };
}

/**
 * Generates all business days (Mon-Fri) between two dates
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @returns {Array} Array of dates in YYYY-MM-DD format
 */
function getBusinessDaysBetweenDates(startDate, endDate) {
  const businessDays = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Set hours to 0 to avoid time comparison issues
  start.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);

  // Determine if we're going forward or backward in time
  const goingForward = start <= end;

  if (goingForward) {
    // Process dates from start to end (forward)
    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();

      // 0 is Sunday, 6 is Saturday - so 1-5 are business days
      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        // Format date as YYYY-MM-DD
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');

        businessDays.push(`${year}-${month}-${day}`);
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }
  } else {
    // Process dates from start to end (backward)
    const currentDate = new Date(start);
    while (currentDate >= end) {
      const dayOfWeek = currentDate.getDay();

      // 0 is Sunday, 6 is Saturday - so 1-5 are business days
      if (dayOfWeek >= 1 && dayOfWeek <= 5) {
        // Format date as YYYY-MM-DD
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');

        businessDays.push(`${year}-${month}-${day}`);
      }

      // Move to previous day
      currentDate.setDate(currentDate.getDate() - 1);
    }
  }

  return businessDays;
}

/**
 * Formats time in seconds to a human-readable string
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string (e.g., "2h 30m 45s")
 */
function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return `${hours}h ${minutes}m ${secs}s`;
}

/**
 * Creates a promise that resolves after the specified time
 * @param {number} ms - Time to wait in milliseconds
 * @returns {Promise} Promise that resolves after the specified time
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

module.exports = {
  createLogger,
  getBusinessDaysBetweenDates,
  formatTime,
  sleep
};
