/**
 * Configuration settings for the Interactive Brokers statement downloader
 */

const path = require("path");
const fs = require("fs");

// Create downloads directory if it doesn't exist
const downloadsDir = path.join(__dirname, "downloads");
if (!fs.existsSync(downloadsDir)) {
  fs.mkdirSync(downloadsDir);
}

// URLs
const URLS = {
  START_PAGE: "https://www.interactivebrokers.co.uk/sso/Login?RL=1",
  STATEMENTS_PAGE:
    "https://www.interactivebrokers.co.uk/AccountManagement/AmAuthentication?action=Statements",
};

// Selectors
const SELECTORS = {
  STATEMENT_BUTTON:
    "#Statements > ng-include > div > div.col-12.col-sm-12.col-md-12.col-lg-8 > section > div.panel-body.panel-padding > div > div > div > div:nth-child(1) > div.col-4.col-sm-3.col-md-3.col-lg-3 > p > span > a",
  DATE_INPUT:
    "#amModalBody > div > div > div > div > div:nth-child(3) > div > div.col-12.col-sm-7.col-md-7.col-lg-7 > am-date-picker > input",
  CSV_DOWNLOAD_BUTTON:
    "#amModalBody > div > div > div > div > div:nth-child(5) > div > div:nth-child(4) > div.col-12.col-sm-7.col-md-7.col-lg-7 > p > span > a",
  PDF_DOWNLOAD_BUTTON:
    "#amModalBody > div > div > div > div > div:nth-child(5) > div > div:nth-child(2) > div.col-12.col-sm-7.col-md-7.col-lg-7 > p > span > a:nth-child(2)",
  NO_STATEMENT_WARNING: ".alert.alert-warning p",
  NO_STATEMENT_WARNING_TEXT:
    "There is no statement available for the account(s) and date(s) selected.",
};

// Timeouts
const TIMEOUTS = {
  PAGE_NAVIGATION: 60000,
  PAGE_LOAD: 30000,
  DOWNLOAD: 5000,
  RETRY_DELAY: 5000,
};

// Browser settings
const BROWSER_CONFIG = {
  headless: false,
  slowMo: 100,
};

// Date range (YYYY-MM-DD format)
// The application will process dates from startDate to endDate
// If startDate is later than endDate, it will process dates in reverse chronological order
const DATE_RANGE = {
  startDate: "2025-05-01", // First date to process
  endDate: "2025-05-28", // Last date to process
};

// Retry settings
const MAX_RETRIES = 3;

module.exports = {
  downloadsDir,
  URLS,
  SELECTORS,
  TIMEOUTS,
  BROWSER_CONFIG,
  DATE_RANGE,
  MAX_RETRIES,
};
