/**
 * Statement downloading logic for the Interactive Brokers statement downloader
 */

const fs = require("fs");
const path = require("path");
const {
  URLS,
  SELECTORS,
  TIMEOUTS,
  downloadsDir,
  MAX_RETRIES,
} = require("./config");
const { sleep } = require("./utils");

/**
 * Checks if a file already exists in the downloads directory
 * @param {string} fileName - Name of the file to check
 * @returns {boolean} True if the file exists, false otherwise
 */
function fileExists(fileName) {
  const filePath = path.join(downloadsDir, fileName);
  return fs.existsSync(filePath);
}

/**
 * Creates a marker file to indicate no statement is available for a date
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} fileName - Name of the marker file
 * @param {Object} logger - Logger object
 */
function createNoStatementMarker(date, fileName, logger) {
  const savePath = path.join(downloadsDir, fileName);

  // Write a note to the file
  fs.writeFileSync(savePath, `No statement available for date: ${date}\n`);

  logger.log(`Created marker file: ${fileName}`);
  logger.logNoStatementAvailable(date);
}

/**
 * Navigates to the statement page and fills in the date
 * @param {Object} page - Playwright page object
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {Object} logger - Logger object
 */
async function navigateToStatementPage(page, date, logger) {
  // Navigate to the statements page
  logger.log("Navigating to statements page...");
  await page.goto(URLS.STATEMENTS_PAGE, {
    timeout: TIMEOUTS.PAGE_NAVIGATION,
  });

  // Wait for page to load
  logger.log("Waiting for page to load...");
  await page.waitForLoadState("networkidle", {
    timeout: TIMEOUTS.PAGE_LOAD,
  });

  // Click on the specified button to access statement download form
  logger.log("Clicking on statement button...");
  await page.waitForSelector(SELECTORS.STATEMENT_BUTTON, {
    timeout: TIMEOUTS.PAGE_LOAD,
  });
  await page.click(SELECTORS.STATEMENT_BUTTON);

  // Wait for modal to appear
  logger.log("Waiting for modal to appear...");
  await page.waitForSelector(SELECTORS.DATE_INPUT, {
    timeout: TIMEOUTS.PAGE_LOAD,
  });

  // Fill in the date input
  logger.log(`Filling in date: ${date}`);
  await page.fill(SELECTORS.DATE_INPUT, date);
}

/**
 * Checks if a statement is available for the specified date
 * @param {Object} page - Playwright page object
 * @returns {Promise<boolean>} Promise resolving to true if no statement is available, false otherwise
 */
async function checkNoStatementWarning(page) {
  const warningElement = await page.$(SELECTORS.NO_STATEMENT_WARNING);

  if (warningElement) {
    const warningText = await warningElement.textContent();

    if (warningText.includes(SELECTORS.NO_STATEMENT_WARNING_TEXT)) {
      return true;
    }
  }

  return false;
}

/**
 * Downloads a CSV statement
 * @param {Object} page - Playwright page object
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} fileName - Name to save the file as
 * @param {Object} logger - Logger object
 * @returns {Promise<boolean>} Promise resolving to true if download was successful, false otherwise
 */
async function downloadCSV(page, date, fileName, logger) {
  try {
    logger.log("Clicking CSV download button...");

    const [download] = await Promise.all([
      page.waitForEvent("download", {
        timeout: TIMEOUTS.DOWNLOAD,
      }),
      page.click(SELECTORS.CSV_DOWNLOAD_BUTTON),
    ]);

    const filePath = await download.path();
    const savePath = path.join(downloadsDir, fileName);
    fs.copyFileSync(filePath, savePath);

    logger.log(`Downloaded CSV statement for ${date} saved as ${fileName}`);
    return true;
  } catch (error) {
    logger.error(`Error downloading CSV for ${date}: ${error.message}`);
    return false;
  }
}

/**
 * Downloads a PDF statement
 * @param {Object} page - Playwright page object
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} fileName - Name to save the file as
 * @param {Object} logger - Logger object
 * @returns {Promise<boolean>} Promise resolving to true if download was successful, false otherwise
 */
async function downloadPDF(page, date, fileName, logger) {
  try {
    logger.log("Clicking PDF download button...");

    const [download] = await Promise.all([
      page.waitForEvent("download", {
        timeout: TIMEOUTS.DOWNLOAD,
      }),
      page.click(SELECTORS.PDF_DOWNLOAD_BUTTON),
    ]);

    const filePath = await download.path();
    const savePath = path.join(downloadsDir, fileName);
    fs.copyFileSync(filePath, savePath);

    logger.log(`Downloaded PDF statement for ${date} saved as ${fileName}`);
    return true;
  } catch (error) {
    logger.error(`Error downloading PDF for ${date}: ${error.message}`);
    return false;
  }
}

/**
 * Downloads a statement for a specific date
 * @param {Object} page - Playwright page object
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {Object} logger - Logger object
 * @returns {Object} Object with success status and whether no statement was available
 */
async function downloadStatement(page, date, logger) {
  let retryCount = 0;
  let success = false;

  // Define file names
  const csvFileName = `IB_Statement_${date}.csv`;
  const pdfFileName = `IB_Statement_${date}.pdf`;
  const noStatementFileName = `IB_Statement_${date}_NO_STATEMENT_AVAILABLE.txt`;

  // Check if files already exist
  const csvExists = fileExists(csvFileName);
  const pdfExists = fileExists(pdfFileName);
  const noStatementExists = fileExists(noStatementFileName);

  // If both files exist or no statement marker exists, skip downloading
  if ((csvExists && pdfExists) || noStatementExists) {
    logger.log(`Files for date ${date} already exist, skipping download.`);
    return { success: true, noStatementAvailable: noStatementExists };
  }

  while (retryCount < MAX_RETRIES && !success) {
    try {
      if (retryCount > 0) {
        logger.log(`Retry attempt ${retryCount} for date ${date}...`);
      }

      // Navigate to statement page and fill in date
      await navigateToStatementPage(page, date, logger);

      // Check if there's a warning about no statement available
      const noStatementAvailable = await checkNoStatementWarning(page);

      if (noStatementAvailable) {
        logger.log(`No statement available for date: ${date}`);
        createNoStatementMarker(date, noStatementFileName, logger);
        return { success: true, noStatementAvailable: true }; // Exit the function early
      }

      // If we get here, there should be a statement to download
      let csvDownloaded = csvExists;
      let pdfDownloaded = pdfExists;

      // Download CSV if it doesn't exist
      if (!csvDownloaded) {
        try {
          csvDownloaded = await downloadCSV(page, date, csvFileName, logger);
        } catch (error) {
          logger.error(`Error downloading CSV for ${date}: ${error.message}`);
        }
      } else {
        logger.log(
          `CSV file for date ${date} already exists, skipping CSV download.`
        );
      }

      await sleep(2000);

      // Download PDF if it doesn't exist
      if (!pdfDownloaded) {
        try {
          pdfDownloaded = await downloadPDF(page, date, pdfFileName, logger);
        } catch (error) {
          logger.error(`Error downloading PDF for ${date}: ${error.message}`);
        }
      } else {
        logger.log(
          `PDF file for date ${date} already exists, skipping PDF download.`
        );
      }

      await sleep(2000);

      // Check if we need to check for warning again
      if (!csvDownloaded && !pdfDownloaded) {
        // Check again for the warning message, as it might appear after clicking the download buttons
        const noStatementAvailable = await checkNoStatementWarning(page);

        if (noStatementAvailable) {
          logger.log(
            `No statement available for date: ${date} (detected after download attempt)`
          );
          createNoStatementMarker(date, noStatementFileName, logger);
          return { success: true, noStatementAvailable: true }; // Exit the function early
        }

        // If we get here and neither file was downloaded, it's a real error
        throw new Error("Failed to download both CSV and PDF files");
      }

      // If we downloaded at least one file type, consider it a success
      if (csvDownloaded || pdfDownloaded) {
        success = true;
      } else {
        throw new Error("Failed to download both CSV and PDF files");
      }
    } catch (error) {
      retryCount++;
      logger.error(
        `Error processing date ${date} (attempt ${retryCount}/${MAX_RETRIES}): ${error.message}`
      );

      if (retryCount >= MAX_RETRIES) {
        logger.log(
          `Failed to download statement for ${date} after ${MAX_RETRIES} attempts.`
        );
        logger.logFailedDownload(date);
      } else {
        // Wait before retrying
        logger.log(
          `Waiting ${TIMEOUTS.RETRY_DELAY / 1000} seconds before retry...`
        );
        await sleep(TIMEOUTS.RETRY_DELAY);
      }
    }
  }

  return { success, noStatementAvailable: false };
}

module.exports = {
  downloadStatement,
};
