/**
 * Progress tracking and user interaction for the Interactive Brokers statement downloader
 */

const { formatTime } = require('./utils');

/**
 * Creates a progress tracker
 * @param {Array} dates - Array of dates to process
 * @param {Object} logger - Logger object
 * @returns {Object} Progress tracker object
 */
function createProgressTracker(dates, logger) {
  let completed = 0;
  let successful = 0;
  let noStatementDates = 0;
  let startTime = Date.now();
  let pauseRequested = false;
  let currentIndex = 0;

  /**
   * Shows the current progress
   */
  function showProgress() {
    const elapsed = (Date.now() - startTime) / 1000; // seconds
    const avgTimePerDownload = completed > 0 ? elapsed / completed : 0;
    const remaining = dates.length - completed;
    const estimatedTimeRemaining = avgTimePerDownload * remaining;

    const timeString = formatTime(estimatedTimeRemaining);

    logger.log(`\nProgress: ${completed}/${dates.length} (${Math.round(completed/dates.length*100)}%)`);
    logger.log(`Success rate: ${successful}/${completed} (${noStatementDates} dates with no statements available)`);
    logger.log(`Estimated time remaining: ${timeString}\n`);
  }

  /**
   * Sets up keyboard listeners for pause/resume and quit
   */
  function setupKeyboardListeners() {
    process.stdin.setRawMode(true);
    process.stdin.resume();

    process.stdin.on('data', (key) => {
      // Press 'p' to pause/resume
      if (key.toString() === 'p') {
        pauseRequested = !pauseRequested;
        if (pauseRequested) {
          logger.log('\n*** Download paused. Press p again to resume. ***\n');
        } else {
          logger.log('\n*** Download resumed. ***\n');
        }
      }

      // Press 'q' to quit
      if (key.toString() === 'q') {
        logger.log('\n*** Download stopped by user. ***\n');
        process.exit(0);
      }
    });

    logger.log('\n*** Download started. Press p to pause/resume, q to quit. ***\n');
  }

  /**
   * Resets keyboard handling
   */
  function resetKeyboardHandling() {
    process.stdin.setRawMode(false);
    process.stdin.pause();
  }

  return {
    get completed() { return completed; },
    get successful() { return successful; },
    get noStatementDates() { return noStatementDates; },
    get currentIndex() { return currentIndex; },
    get isPaused() { return pauseRequested; },
    get totalDates() { return dates.length; },

    incrementCompleted(wasSuccessful, isNoStatement = false) {
      completed++;
      if (wasSuccessful) successful++;
      if (isNoStatement) noStatementDates++;
    },

    incrementIndex() {
      currentIndex++;
    },

    showProgress,
    setupKeyboardListeners,
    resetKeyboardHandling
  };
}

module.exports = {
  createProgressTracker
};
